import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ride_sharing/main.dart';

void main() {
  group('Smart Ride Sharing App Tests', () {
    testWidgets('App should display title and buttons', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(SmartRideApp());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify that the app title is displayed
      expect(find.text('Smart Ride Sharing'), findsOneWidget);

      // Verify that all three buttons are present
      expect(find.text('Take a Ride'), findsOneWidget);
      expect(find.text('Backidge'), findsOneWidget);
      expect(find.text('Ride Sharing'), findsOneWidget);

      // Verify that icons are present
      expect(find.byIcon(Icons.directions_car), findsOneWidget);
      expect(find.byIcon(Icons.backpack), findsOneWidget);
      expect(find.byIcon(Icons.group), findsOneWidget);
    });

    testWidgets('Buttons should show SnackBar when tapped', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(SmartRideApp());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Tap the "Take a Ride" button
      await tester.tap(find.text('Take a Ride'));
      await tester.pumpAndSettle();

      // Verify that a SnackBar is shown
      expect(find.text('Take a Ride feature coming soon!'), findsOneWidget);

      // Wait for SnackBar to disappear
      await tester.pumpAndSettle(Duration(seconds: 3));

      // Tap the "Ride Sharing" button (primary button)
      await tester.tap(find.text('Ride Sharing'));
      await tester.pumpAndSettle();

      // Verify that a SnackBar is shown
      expect(find.text('Ride Sharing feature coming soon!'), findsOneWidget);
    });

    testWidgets('App should have correct theme colors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(SmartRideApp());

      // Wait for animations to complete
      await tester.pumpAndSettle();

      // Verify that the app has the correct background color
      final Scaffold scaffold = tester.widget(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(Colors.lightBlue[50]));
    });
  });
}
