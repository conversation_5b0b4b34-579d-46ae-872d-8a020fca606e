enum PaymentType {
  creditCard,
  debitCard,
  digitalWallet,
  cash,
  bankTransfer,
}

enum PaymentStatus {
  pending,
  processing,
  completed,
  failed,
  refunded,
}

class PaymentMethodModel {
  final String id;
  final PaymentType type;
  final String? cardNumber;
  final String? cardHolderName;
  final String? expiryDate;
  final String? cvv;
  final String? brand; // Visa, Mastercard, etc.
  final String? walletName; // PayPal, Apple Pay, etc.
  final String? email; // For digital wallets
  bool isDefault;

  PaymentMethodModel({
    required this.id,
    required this.type,
    this.cardNumber,
    this.cardHolderName,
    this.expiryDate,
    this.cvv,
    this.brand,
    this.walletName,
    this.email,
    this.isDefault = false,
  });

  PaymentMethodModel copyWith({
    String? id,
    PaymentType? type,
    String? cardNumber,
    String? cardHolderName,
    String? expiryDate,
    String? cvv,
    String? brand,
    String? walletName,
    String? email,
    bool? isDefault,
  }) {
    return PaymentMethodModel(
      id: id ?? this.id,
      type: type ?? this.type,
      cardNumber: cardNumber ?? this.cardNumber,
      cardHolderName: cardHolderName ?? this.cardHolderName,
      expiryDate: expiryDate ?? this.expiryDate,
      cvv: cvv ?? this.cvv,
      brand: brand ?? this.brand,
      walletName: walletName ?? this.walletName,
      email: email ?? this.email,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  String get displayName {
    switch (type) {
      case PaymentType.creditCard:
      case PaymentType.debitCard:
        return '$brand ${cardNumber?.substring(cardNumber!.length - 4) ?? '****'}';
      case PaymentType.digitalWallet:
        return walletName ?? 'Digital Wallet';
      case PaymentType.cash:
        return 'Cash';
      case PaymentType.bankTransfer:
        return 'Bank Transfer';
    }
  }

  String get typeText {
    switch (type) {
      case PaymentType.creditCard:
        return 'Credit Card';
      case PaymentType.debitCard:
        return 'Debit Card';
      case PaymentType.digitalWallet:
        return 'Digital Wallet';
      case PaymentType.cash:
        return 'Cash';
      case PaymentType.bankTransfer:
        return 'Bank Transfer';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'cardNumber': cardNumber,
      'cardHolderName': cardHolderName,
      'expiryDate': expiryDate,
      'cvv': cvv,
      'brand': brand,
      'walletName': walletName,
      'email': email,
      'isDefault': isDefault,
    };
  }

  factory PaymentMethodModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodModel(
      id: json['id'],
      type: PaymentType.values.firstWhere(
        (e) => e.toString() == json['type'],
      ),
      cardNumber: json['cardNumber'],
      cardHolderName: json['cardHolderName'],
      expiryDate: json['expiryDate'],
      cvv: json['cvv'],
      brand: json['brand'],
      walletName: json['walletName'],
      email: json['email'],
      isDefault: json['isDefault'] ?? false,
    );
  }
}

class PaymentHistoryModel {
  final String id;
  final String rideId;
  final double amount;
  final PaymentMethodModel paymentMethod;
  final PaymentStatus status;
  final DateTime timestamp;
  final String? description;
  final String? transactionId;
  final double? tip;

  PaymentHistoryModel({
    required this.id,
    required this.rideId,
    required this.amount,
    required this.paymentMethod,
    required this.status,
    required this.timestamp,
    this.description,
    this.transactionId,
    this.tip,
  });

  PaymentHistoryModel copyWith({
    String? id,
    String? rideId,
    double? amount,
    PaymentMethodModel? paymentMethod,
    PaymentStatus? status,
    DateTime? timestamp,
    String? description,
    String? transactionId,
    double? tip,
  }) {
    return PaymentHistoryModel(
      id: id ?? this.id,
      rideId: rideId ?? this.rideId,
      amount: amount ?? this.amount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      description: description ?? this.description,
      transactionId: transactionId ?? this.transactionId,
      tip: tip ?? this.tip,
    );
  }

  double get totalAmount {
    return amount + (tip ?? 0.0);
  }

  String get statusText {
    switch (status) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.refunded:
        return 'Refunded';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rideId': rideId,
      'amount': amount,
      'paymentMethod': paymentMethod.toJson(),
      'status': status.toString(),
      'timestamp': timestamp.toIso8601String(),
      'description': description,
      'transactionId': transactionId,
      'tip': tip,
    };
  }

  factory PaymentHistoryModel.fromJson(Map<String, dynamic> json) {
    return PaymentHistoryModel(
      id: json['id'],
      rideId: json['rideId'],
      amount: json['amount'].toDouble(),
      paymentMethod: PaymentMethodModel.fromJson(json['paymentMethod']),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
      ),
      timestamp: DateTime.parse(json['timestamp']),
      description: json['description'],
      transactionId: json['transactionId'],
      tip: json['tip']?.toDouble(),
    );
  }
}
