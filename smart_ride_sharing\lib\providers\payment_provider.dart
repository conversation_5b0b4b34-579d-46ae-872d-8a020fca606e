import 'package:flutter/material.dart';
import '../models/payment_model.dart';

class PaymentProvider with ChangeNotifier {
  List<PaymentMethodModel> _paymentMethods = [];
  PaymentMethodModel? _selectedPaymentMethod;
  List<PaymentHistoryModel> _paymentHistory = [];
  bool _isProcessingPayment = false;

  // Getters
  List<PaymentMethodModel> get paymentMethods => _paymentMethods;
  PaymentMethodModel? get selectedPaymentMethod => _selectedPaymentMethod;
  List<PaymentHistoryModel> get paymentHistory => _paymentHistory;
  bool get isProcessingPayment => _isProcessingPayment;

  PaymentProvider() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock payment methods
    _paymentMethods = [
      PaymentMethodModel(
        id: '1',
        type: PaymentType.creditCard,
        cardNumber: '**** **** **** 1234',
        cardHolderName: '<PERSON>',
        expiryDate: '12/25',
        isDefault: true,
        brand: 'Visa',
      ),
      PaymentMethodModel(
        id: '2',
        type: PaymentType.creditCard,
        cardNumber: '**** **** **** 5678',
        cardHolderName: 'John Doe',
        expiryDate: '08/26',
        isDefault: false,
        brand: 'Mastercard',
      ),
      PaymentMethodModel(
        id: '3',
        type: PaymentType.digitalWallet,
        walletName: 'PayPal',
        email: '<EMAIL>',
        isDefault: false,
      ),
      PaymentMethodModel(
        id: '4',
        type: PaymentType.cash,
        isDefault: false,
      ),
    ];

    _selectedPaymentMethod = _paymentMethods.first;

    // Mock payment history
    _paymentHistory = [
      PaymentHistoryModel(
        id: '1',
        rideId: '1',
        amount: 15.50,
        paymentMethod: _paymentMethods[0],
        status: PaymentStatus.completed,
        timestamp: DateTime.now().subtract(Duration(days: 2)),
        description: 'Ride from Downtown to Uptown',
      ),
      PaymentHistoryModel(
        id: '2',
        rideId: '2',
        amount: 22.75,
        paymentMethod: _paymentMethods[1],
        status: PaymentStatus.completed,
        timestamp: DateTime.now().subtract(Duration(days: 5)),
        description: 'Shared ride from Midtown to Downtown',
      ),
    ];
  }

  void selectPaymentMethod(PaymentMethodModel paymentMethod) {
    _selectedPaymentMethod = paymentMethod;
    notifyListeners();
  }

  Future<bool> processPayment(double amount, String rideId) async {
    if (_selectedPaymentMethod == null) return false;

    _isProcessingPayment = true;
    notifyListeners();

    try {
      // Simulate payment processing
      await Future.delayed(Duration(seconds: 3));

      // Create payment history record
      PaymentHistoryModel payment = PaymentHistoryModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        rideId: rideId,
        amount: amount,
        paymentMethod: _selectedPaymentMethod!,
        status: PaymentStatus.completed,
        timestamp: DateTime.now(),
        description: 'Ride payment',
      );

      _paymentHistory.insert(0, payment);
      
      _isProcessingPayment = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isProcessingPayment = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> addPaymentMethod(PaymentMethodModel paymentMethod) async {
    try {
      // Simulate API call
      await Future.delayed(Duration(seconds: 1));
      
      _paymentMethods.add(paymentMethod);
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> removePaymentMethod(String paymentMethodId) async {
    try {
      // Simulate API call
      await Future.delayed(Duration(seconds: 1));
      
      _paymentMethods.removeWhere((method) => method.id == paymentMethodId);
      
      // If removed method was selected, select first available
      if (_selectedPaymentMethod?.id == paymentMethodId && _paymentMethods.isNotEmpty) {
        _selectedPaymentMethod = _paymentMethods.first;
      }
      
      notifyListeners();
      return true;
    } catch (e) {
      return false;
    }
  }

  void setDefaultPaymentMethod(String paymentMethodId) {
    // Remove default from all methods
    for (var method in _paymentMethods) {
      method.isDefault = false;
    }
    
    // Set new default
    PaymentMethodModel? method = _paymentMethods.firstWhere(
      (method) => method.id == paymentMethodId,
    );
    method.isDefault = true;
    
    _selectedPaymentMethod = method;
    notifyListeners();
  }

  double calculateTip(double fareAmount, double tipPercentage) {
    return fareAmount * (tipPercentage / 100);
  }

  double calculateTotal(double fareAmount, double tipAmount) {
    return fareAmount + tipAmount;
  }

  PaymentMethodModel? getPaymentMethodById(String id) {
    try {
      return _paymentMethods.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  List<PaymentHistoryModel> getPaymentHistoryForRide(String rideId) {
    return _paymentHistory.where((payment) => payment.rideId == rideId).toList();
  }
}
