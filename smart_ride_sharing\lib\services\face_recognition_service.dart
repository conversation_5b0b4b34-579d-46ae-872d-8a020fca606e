import 'dart:async';
import 'dart:math';
import '../models/face_recognition_model.dart';
import '../models/user_model.dart';

class FaceRecognitionService {
  static const double GENDER_DETECTION_THRESHOLD = 0.75;
  static const double VERIFICATION_THRESHOLD = 0.85;
  static const int PROCESSING_DELAY_MS = 2000;

  /// 🔍 Advanced Face Recognition with Gender Detection
  /// Simulates real face recognition API with gender-based safety features
  static Future<FaceRecognitionResult> performFaceRecognition({
    required String userId,
    required String imagePath,
    String? expectedGender,
  }) async {
    print('🔍 Starting face recognition for user: $userId');
    print('📸 Processing image: $imagePath');
    
    // Simulate processing delay
    await Future.delayed(Duration(milliseconds: PROCESSING_DELAY_MS));

    // Simulate face detection and gender recognition
    final Random random = Random();
    
    // Simulate gender detection (in real app, this would use ML model)
    List<String> genders = ['male', 'female'];
    String detectedGender = expectedGender ?? genders[random.nextInt(genders.length)];
    double confidence = 0.7 + (random.nextDouble() * 0.25); // 0.7 to 0.95
    
    // Simulate verification success rate
    bool isVerified = confidence >= VERIFICATION_THRESHOLD;
    
    // Generate mock biometric data
    Map<String, dynamic> biometricData = {
      'faceEncoding': List.generate(128, (index) => random.nextDouble()),
      'landmarks': {
        'leftEye': [random.nextDouble() * 100, random.nextDouble() * 100],
        'rightEye': [random.nextDouble() * 100, random.nextDouble() * 100],
        'nose': [random.nextDouble() * 100, random.nextDouble() * 100],
        'mouth': [random.nextDouble() * 100, random.nextDouble() * 100],
      },
      'faceQuality': 0.8 + (random.nextDouble() * 0.2),
      'lightingCondition': 'good',
      'imageResolution': '1920x1080',
    };

    print('✅ Face recognition completed');
    print('👤 Detected gender: $detectedGender');
    print('🎯 Confidence: ${(confidence * 100).toStringAsFixed(1)}%');
    print('✅ Verification: ${isVerified ? "PASSED" : "FAILED"}');

    return FaceRecognitionResult(
      userId: userId,
      detectedGender: detectedGender,
      confidence: confidence,
      isVerified: isVerified,
      photoPath: imagePath,
      timestamp: DateTime.now(),
      biometricData: biometricData,
    );
  }

  /// 🛡️ Gender-based Safety Matching
  /// Core safety feature for Smart Ride Sharing
  static bool isGenderCompatible({
    required String userGender,
    required List<String> existingRiderGenders,
    required SafetyPreferences safetyPrefs,
  }) {
    print('🛡️ Checking gender compatibility...');
    print('👤 User gender: $userGender');
    print('👥 Existing riders: $existingRiderGenders');
    print('⚙️ Safety preferences: ${safetyPrefs.enableGenderBasedGrouping}');

    // If gender-based grouping is disabled, allow all
    if (!safetyPrefs.enableGenderBasedGrouping) {
      print('✅ Gender grouping disabled - allowing ride');
      return true;
    }

    // If mixed gender rides are allowed
    if (safetyPrefs.allowMixedGenderRides) {
      print('✅ Mixed gender rides allowed');
      return true;
    }

    // Check if all riders have the same gender
    bool allSameGender = existingRiderGenders.every((gender) => gender == userGender);
    
    if (allSameGender) {
      print('✅ All riders have same gender - compatible');
      return true;
    } else {
      print('❌ Mixed genders detected - not compatible');
      return false;
    }
  }

  /// 🚨 Safety Alert System
  static Future<List<SafetyAlert>> checkSafetyAlerts({
    required String userId,
    required String rideId,
    required List<UserModel> rideParticipants,
  }) async {
    List<SafetyAlert> alerts = [];
    final Random random = Random();

    // Simulate safety checks
    await Future.delayed(Duration(milliseconds: 500));

    // Check for low-rated participants
    for (UserModel participant in rideParticipants) {
      if (participant.rating < 4.0) {
        alerts.add(SafetyAlert(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          type: 'low_rating',
          message: 'Rider ${participant.name} has a low safety rating (${participant.rating}/5.0)',
          timestamp: DateTime.now(),
          severity: 'medium',
          metadata: {
            'participantId': participant.id,
            'rating': participant.rating,
          },
        ));
      }
    }

    // Simulate random safety alerts (for demo)
    if (random.nextDouble() < 0.1) { // 10% chance
      alerts.add(SafetyAlert(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: 'route_deviation',
        message: 'Driver has deviated from the planned route',
        timestamp: DateTime.now(),
        severity: 'high',
        metadata: {
          'rideId': rideId,
          'deviationDistance': random.nextDouble() * 2.0,
        },
      ));
    }

    print('🚨 Safety check completed: ${alerts.length} alerts found');
    return alerts;
  }

  /// 📱 Emergency Contact System
  static Future<bool> triggerEmergencyAlert({
    required String userId,
    required String rideId,
    required List<String> emergencyContacts,
    String? customMessage,
  }) async {
    print('🚨 EMERGENCY ALERT TRIGGERED');
    print('👤 User: $userId');
    print('🚗 Ride: $rideId');
    print('📞 Contacts: ${emergencyContacts.length}');

    // Simulate emergency alert processing
    await Future.delayed(Duration(milliseconds: 1000));

    // In a real app, this would:
    // 1. Send SMS/calls to emergency contacts
    // 2. Alert local authorities if needed
    // 3. Share live location
    // 4. Record incident for investigation

    String alertMessage = customMessage ?? 
        'EMERGENCY: I need help during my ride. Please check my location and contact me immediately.';

    print('📨 Emergency message: $alertMessage');
    print('✅ Emergency alerts sent successfully');

    return true;
  }

  /// 🔐 Biometric Verification
  static Future<bool> verifyBiometricMatch({
    required FaceRecognitionResult currentScan,
    required FaceRecognitionResult storedBiometric,
  }) async {
    print('🔐 Verifying biometric match...');
    
    // Simulate biometric comparison
    await Future.delayed(Duration(milliseconds: 800));

    // In a real app, this would compare face encodings
    List<double> currentEncoding = List<double>.from(currentScan.biometricData['faceEncoding']);
    List<double> storedEncoding = List<double>.from(storedBiometric.biometricData['faceEncoding']);

    // Simulate euclidean distance calculation
    double distance = _calculateEuclideanDistance(currentEncoding, storedEncoding);
    bool isMatch = distance < 0.6; // Threshold for face match

    print('📊 Biometric distance: ${distance.toStringAsFixed(3)}');
    print('✅ Match result: ${isMatch ? "VERIFIED" : "FAILED"}');

    return isMatch;
  }

  /// Helper method to calculate euclidean distance
  static double _calculateEuclideanDistance(List<double> encoding1, List<double> encoding2) {
    if (encoding1.length != encoding2.length) return double.infinity;
    
    double sum = 0.0;
    for (int i = 0; i < encoding1.length; i++) {
      sum += pow(encoding1[i] - encoding2[i], 2);
    }
    return sqrt(sum);
  }

  /// 📊 Safety Score Calculation
  static double calculateSafetyScore({
    required UserModel user,
    required List<UserModel> rideParticipants,
    required SafetyPreferences safetyPrefs,
    FaceRecognitionResult? faceVerification,
  }) {
    double score = 0.0;

    // Base score from user rating
    score += (user.rating / 5.0) * 0.3;

    // Face verification bonus
    if (faceVerification != null && faceVerification.isVerified) {
      score += 0.2;
    }

    // Gender compatibility bonus
    List<String> genders = rideParticipants.map((p) => p.gender ?? 'unknown').toList();
    if (isGenderCompatible(
      userGender: user.gender ?? 'unknown',
      existingRiderGenders: genders,
      safetyPrefs: safetyPrefs,
    )) {
      score += 0.2;
    }

    // Ride history bonus
    if (user.totalRides > 50) {
      score += 0.15;
    } else if (user.totalRides > 20) {
      score += 0.1;
    }

    // Emergency contacts setup bonus
    if (safetyPrefs.emergencyContactNumbers.isNotEmpty) {
      score += 0.15;
    }

    return (score * 100).clamp(0.0, 100.0);
  }
}
