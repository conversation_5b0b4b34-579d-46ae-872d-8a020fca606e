import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ride_sharing/services/ai_engine.dart';
import 'package:smart_ride_sharing/models/ride_model.dart';
import 'package:smart_ride_sharing/models/driver_model.dart';
import 'package:smart_ride_sharing/models/location_model.dart';
import 'package:smart_ride_sharing/models/ai_match_result.dart';

void main() {
  group('Enhanced AI Engine Tests', () {
    late LocationModel pickupLocation;
    late LocationModel dropoffLocation;
    late DriverModel testDriver;
    late List<RideModel> availableRides;
    late List<DriverModel> availableDrivers;

    setUp(() {
      // Setup test data
      pickupLocation = LocationModel(
        address: '123 Main St, Downtown',
        latitude: 40.7128,
        longitude: -74.0060,
        city: 'New York',
        state: 'NY',
      );

      dropoffLocation = LocationModel(
        address: '456 Broadway, Midtown',
        latitude: 40.7589,
        longitude: -73.9851,
        city: 'New York',
        state: 'NY',
      );

      testDriver = DriverModel(
        id: 'driver_1',
        name: '<PERSON>',
        rating: 4.8,
        photoUrl: 'https://example.com/photo.jpg',
        vehicleModel: 'Toyota Camry',
        licensePlate: 'ABC123',
        phoneNumber: '+**********',
        currentLocation: pickupLocation,
        estimatedArrival: 5,
        isAvailable: true,
        totalTrips: 150,
        vehicleColor: 'Blue',
        vehicleYear: '2020',
        isVerified: true,
      );

      availableRides = [
        RideModel(
          id: 'ride_1',
          pickupLocation: LocationModel(
            address: '120 Main St, Downtown',
            latitude: 40.7125,
            longitude: -74.0055,
          ),
          dropoffLocation: LocationModel(
            address: '450 Broadway, Midtown',
            latitude: 40.7585,
            longitude: -73.9845,
          ),
          driver: testDriver,
          fare: 25.0,
          distance: 5.2,
          duration: 15,
          status: RideStatus.searching,
          rideType: RideType.shared,
          createdAt: DateTime.now(),
          sharedRiders: ['Alice'],
        ),
      ];

      availableDrivers = [testDriver];
    });

    test('AI Engine should find optimal ride match', () async {
      // Test AI matching functionality
      AIMatchResult result = await AIEngine.findOptimalRideMatch(
        pickup: pickupLocation,
        dropoff: dropoffLocation,
        availableRides: availableRides,
        availableDrivers: availableDrivers,
        userGender: 'female',
        userPreferences: ['eco-friendly'],
        userId: 'user_123',
        userRating: 4.5,
      );

      // Verify AI match result
      expect(result, isNotNull);
      expect(result.selectedRide, isNotNull);
      expect(result.compatibilityScore, greaterThan(0.0));
      expect(result.aiReasons, isNotEmpty);
      expect(result.environmentalImpact, isNotNull);
    });

    test('AI Engine should create individual ride when no shared rides available', () async {
      // Test with empty available rides
      AIMatchResult result = await AIEngine.findOptimalRideMatch(
        pickup: pickupLocation,
        dropoff: dropoffLocation,
        availableRides: [],
        availableDrivers: availableDrivers,
        userGender: 'male',
        userId: 'user_456',
        userRating: 4.2,
      );

      // Verify individual ride creation
      expect(result.matchType, equals(AIMatchType.newRide));
      expect(result.selectedRide.rideType, equals(RideType.individual));
      expect(result.selectedRide.aiMatched, isTrue);
      expect(result.aiReasons, contains(matches(r'AI selected optimal individual ride')));
    });

    test('AI Engine should calculate environmental impact correctly', () async {
      AIMatchResult result = await AIEngine.findOptimalRideMatch(
        pickup: pickupLocation,
        dropoff: dropoffLocation,
        availableRides: availableRides,
        availableDrivers: availableDrivers,
      );

      // Verify environmental impact calculations
      expect(result.environmentalImpact.co2Saved, greaterThanOrEqualTo(0.0));
      expect(result.environmentalImpact.treesEquivalent, greaterThanOrEqualTo(0));
      expect(result.environmentalImpact.fuelSaved, greaterThanOrEqualTo(0.0));
      expect(result.environmentalImpact.impactLevel, isNotEmpty);
    });

    test('AI Engine should handle multiple available rides', () async {
      // Add more test rides
      availableRides.addAll([
        RideModel(
          id: 'ride_2',
          pickupLocation: LocationModel(
            address: '125 Main St, Downtown',
            latitude: 40.7130,
            longitude: -74.0065,
          ),
          dropoffLocation: dropoffLocation,
          driver: testDriver,
          fare: 22.0,
          distance: 4.8,
          duration: 12,
          status: RideStatus.confirmed,
          rideType: RideType.shared,
          createdAt: DateTime.now(),
          sharedRiders: ['Bob'],
        ),
      ]);

      AIMatchResult result = await AIEngine.findOptimalRideMatch(
        pickup: pickupLocation,
        dropoff: dropoffLocation,
        availableRides: availableRides,
        availableDrivers: availableDrivers,
        userGender: 'female',
      );

      // Verify AI selects best match from multiple options
      expect(result.matchType, equals(AIMatchType.sharedRide));
      expect(result.compatibilityScore, greaterThan(0.6)); // Above threshold
    });

    test('AI Engine should provide detailed reasons for matching', () async {
      AIMatchResult result = await AIEngine.findOptimalRideMatch(
        pickup: pickupLocation,
        dropoff: dropoffLocation,
        availableRides: availableRides,
        availableDrivers: availableDrivers,
        userGender: 'female',
        userPreferences: ['safety', 'eco-friendly'],
      );

      // Verify AI provides comprehensive reasons
      expect(result.aiReasons.length, greaterThanOrEqualTo(2));
      expect(result.aiReasons.any((reason) => reason.contains('AI')), isTrue);
      expect(result.aiReasons.any((reason) => reason.contains('compatibility') || reason.contains('match')), isTrue);
    });
  });
}
