import 'dart:async';
import 'dart:math';
import '../models/ride_model.dart';
import '../models/smart_rider_suggestion.dart';
import '../models/user_model.dart';

enum NotificationType {
  riderRequest,
  riderAccepted,
  riderRejected,
  routeOptimized,
  driverArriving,
  rideStarted,
  rideCompleted,
  safetyAlert,
  emergencyAlert,
  aiMatchFound,
  paymentProcessed,
}

class RideNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> data;
  final bool isRead;
  final String priority; // low, medium, high, critical

  RideNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.timestamp,
    required this.data,
    this.isRead = false,
    this.priority = 'medium',
  });

  factory RideNotification.fromJson(Map<String, dynamic> json) {
    return RideNotification(
      id: json['id'],
      type: NotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => NotificationType.riderRequest,
      ),
      title: json['title'],
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      data: json['data'],
      isRead: json['isRead'] ?? false,
      priority: json['priority'] ?? 'medium',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
      'isRead': isRead,
      'priority': priority,
    };
  }

  RideNotification copyWith({
    bool? isRead,
    String? priority,
  }) {
    return RideNotification(
      id: id,
      type: type,
      title: title,
      message: message,
      timestamp: timestamp,
      data: data,
      isRead: isRead ?? this.isRead,
      priority: priority ?? this.priority,
    );
  }

  bool get isHighPriority => priority == 'high' || priority == 'critical';
}

class NotificationService {
  static final StreamController<RideNotification> _notificationController = 
      StreamController<RideNotification>.broadcast();
  
  static Stream<RideNotification> get notificationStream => _notificationController.stream;
  
  static final List<RideNotification> _notifications = [];
  static final Random _random = Random();

  /// 🔔 Smart Ride Sharing Notification System
  /// Handles all notifications for the AI-powered ride sharing features

  /// Send notification when a new rider wants to join a ride
  static Future<void> notifyRiderJoinRequest({
    required String driverId,
    required String riderId,
    required String riderName,
    required SmartRiderSuggestion suggestion,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.riderRequest,
      title: 'New Rider Request',
      message: '$riderName wants to join your ride. Estimated savings: \$${suggestion.costSavings.toStringAsFixed(2)}',
      timestamp: DateTime.now(),
      priority: 'high',
      data: {
        'riderId': riderId,
        'riderName': riderName,
        'suggestion': suggestion.toJson(),
        'timeImpact': suggestion.timeImpact,
        'costSavings': suggestion.costSavings,
      },
    );

    await _sendNotification(notification);
    print('🔔 Rider join request notification sent to driver: $driverId');
  }

  /// Notify when rider request is accepted
  static Future<void> notifyRiderAccepted({
    required String riderId,
    required String driverName,
    required RideModel ride,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.riderAccepted,
      title: 'Ride Request Accepted!',
      message: '$driverName accepted your request. Your ride is confirmed!',
      timestamp: DateTime.now(),
      priority: 'high',
      data: {
        'rideId': ride.id,
        'driverName': driverName,
        'pickupTime': ride.createdAt.add(Duration(minutes: 10)).toIso8601String(),
      },
    );

    await _sendNotification(notification);
    print('🔔 Rider accepted notification sent to: $riderId');
  }

  /// Notify when rider request is rejected
  static Future<void> notifyRiderRejected({
    required String riderId,
    required String reason,
    required List<RideModel> alternativeRides,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.riderRejected,
      title: 'Ride Request Declined',
      message: 'Your request was declined. We found ${alternativeRides.length} alternative rides for you.',
      timestamp: DateTime.now(),
      priority: 'medium',
      data: {
        'reason': reason,
        'alternativeRides': alternativeRides.map((r) => r.toJson()).toList(),
      },
    );

    await _sendNotification(notification);
    print('🔔 Rider rejected notification sent to: $riderId');
  }

  /// Notify when AI optimizes the route
  static Future<void> notifyRouteOptimized({
    required List<String> participantIds,
    required int timeSaved,
    required double costSavings,
    required String optimizationReason,
  }) async {
    for (String participantId in participantIds) {
      final notification = RideNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: NotificationType.routeOptimized,
        title: 'Route Optimized by AI',
        message: 'AI found a better route! Saved ${timeSaved} minutes and \$${costSavings.toStringAsFixed(2)}',
        timestamp: DateTime.now(),
        priority: 'medium',
        data: {
          'timeSaved': timeSaved,
          'costSavings': costSavings,
          'reason': optimizationReason,
        },
      );

      await _sendNotification(notification);
    }
    print('🔔 Route optimization notifications sent to ${participantIds.length} participants');
  }

  /// Notify when AI finds a perfect match
  static Future<void> notifyAIMatchFound({
    required String userId,
    required double compatibilityScore,
    required double estimatedSavings,
    required List<String> aiReasons,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.aiMatchFound,
      title: 'Perfect AI Match Found!',
      message: '${(compatibilityScore * 100).toInt()}% compatibility • Save \$${estimatedSavings.toStringAsFixed(2)}',
      timestamp: DateTime.now(),
      priority: 'high',
      data: {
        'compatibilityScore': compatibilityScore,
        'estimatedSavings': estimatedSavings,
        'aiReasons': aiReasons,
      },
    );

    await _sendNotification(notification);
    print('🔔 AI match notification sent to: $userId');
  }

  /// Notify about safety alerts
  static Future<void> notifySafetyAlert({
    required List<String> participantIds,
    required String alertType,
    required String message,
    required String severity,
  }) async {
    for (String participantId in participantIds) {
      final notification = RideNotification(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        type: NotificationType.safetyAlert,
        title: 'Safety Alert',
        message: message,
        timestamp: DateTime.now(),
        priority: severity == 'high' ? 'critical' : 'high',
        data: {
          'alertType': alertType,
          'severity': severity,
        },
      );

      await _sendNotification(notification);
    }
    print('🔔 Safety alert notifications sent to ${participantIds.length} participants');
  }

  /// Emergency notification system
  static Future<void> notifyEmergency({
    required String userId,
    required String rideId,
    required List<String> emergencyContacts,
    required String location,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.emergencyAlert,
      title: 'EMERGENCY ALERT',
      message: 'Emergency assistance requested. Location shared with emergency contacts.',
      timestamp: DateTime.now(),
      priority: 'critical',
      data: {
        'userId': userId,
        'rideId': rideId,
        'location': location,
        'emergencyContacts': emergencyContacts,
      },
    );

    await _sendNotification(notification);
    print('🚨 Emergency notification sent for user: $userId');
  }

  /// Driver arrival notification
  static Future<void> notifyDriverArriving({
    required String riderId,
    required String driverName,
    required int estimatedMinutes,
  }) async {
    final notification = RideNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      type: NotificationType.driverArriving,
      title: 'Driver Arriving Soon',
      message: '$driverName will arrive in $estimatedMinutes minutes',
      timestamp: DateTime.now(),
      priority: 'high',
      data: {
        'driverName': driverName,
        'estimatedMinutes': estimatedMinutes,
      },
    );

    await _sendNotification(notification);
    print('🔔 Driver arrival notification sent to: $riderId');
  }

  /// Get all notifications for a user
  static List<RideNotification> getNotifications({bool unreadOnly = false}) {
    if (unreadOnly) {
      return _notifications.where((n) => !n.isRead).toList();
    }
    return List.from(_notifications);
  }

  /// Mark notification as read
  static void markAsRead(String notificationId) {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
    }
  }

  /// Clear all notifications
  static void clearAll() {
    _notifications.clear();
  }

  /// Get unread count
  static int get unreadCount => _notifications.where((n) => !n.isRead).length;

  /// Private method to send notification
  static Future<void> _sendNotification(RideNotification notification) async {
    _notifications.insert(0, notification); // Add to beginning
    _notificationController.add(notification);
    
    // Simulate notification delivery delay
    await Future.delayed(Duration(milliseconds: 100));
    
    // In a real app, this would:
    // 1. Send push notification
    // 2. Store in database
    // 3. Send to notification service (FCM, etc.)
  }

  /// Simulate real-time notifications for demo
  static void startDemoNotifications() {
    Timer.periodic(Duration(seconds: 30), (timer) {
      if (_random.nextDouble() < 0.3) { // 30% chance every 30 seconds
        _sendDemoNotification();
      }
    });
  }

  static void _sendDemoNotification() {
    final demoNotifications = [
      () => notifyAIMatchFound(
        userId: 'demo_user',
        compatibilityScore: 0.85 + (_random.nextDouble() * 0.15),
        estimatedSavings: 5.0 + (_random.nextDouble() * 10.0),
        aiReasons: ['Route compatibility', 'Time efficiency', 'Cost optimization'],
      ),
      () => notifyDriverArriving(
        riderId: 'demo_user',
        driverName: 'Demo Driver',
        estimatedMinutes: 2 + _random.nextInt(8),
      ),
      () => notifyRouteOptimized(
        participantIds: ['demo_user'],
        timeSaved: 3 + _random.nextInt(7),
        costSavings: 1.0 + (_random.nextDouble() * 3.0),
        optimizationReason: 'Traffic conditions improved',
      ),
    ];

    final randomNotification = demoNotifications[_random.nextInt(demoNotifications.length)];
    randomNotification();
  }

  /// Dispose resources
  static void dispose() {
    _notificationController.close();
  }
}
