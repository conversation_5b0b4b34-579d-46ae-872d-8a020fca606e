class FaceRecognitionResult {
  final String userId;
  final String detectedGender;
  final double confidence;
  final bool isVerified;
  final String photoPath;
  final DateTime timestamp;
  final Map<String, dynamic> biometricData;

  FaceRecognitionResult({
    required this.userId,
    required this.detectedGender,
    required this.confidence,
    required this.isVerified,
    required this.photoPath,
    required this.timestamp,
    required this.biometricData,
  });

  factory FaceRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FaceRecognitionResult(
      userId: json['userId'],
      detectedGender: json['detectedGender'],
      confidence: json['confidence'].toDouble(),
      isVerified: json['isVerified'],
      photoPath: json['photoPath'],
      timestamp: DateTime.parse(json['timestamp']),
      biometricData: json['biometricData'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'detectedGender': detectedGender,
      'confidence': confidence,
      'isVerified': isVerified,
      'photoPath': photoPath,
      'timestamp': timestamp.toIso8601String(),
      'biometricData': biometricData,
    };
  }

  String get confidenceText {
    if (confidence >= 0.9) return 'Very High';
    if (confidence >= 0.8) return 'High';
    if (confidence >= 0.7) return 'Medium';
    return 'Low';
  }

  bool get isHighConfidence => confidence >= 0.8;
}

class SafetyPreferences {
  final bool enableGenderBasedGrouping;
  final bool requireFaceVerification;
  final bool allowMixedGenderRides;
  final List<String> preferredGenders;
  final double minimumSafetyRating;
  final bool enableEmergencyContacts;
  final List<String> emergencyContactNumbers;

  SafetyPreferences({
    this.enableGenderBasedGrouping = true,
    this.requireFaceVerification = true,
    this.allowMixedGenderRides = false,
    this.preferredGenders = const [],
    this.minimumSafetyRating = 4.5,
    this.enableEmergencyContacts = true,
    this.emergencyContactNumbers = const [],
  });

  factory SafetyPreferences.fromJson(Map<String, dynamic> json) {
    return SafetyPreferences(
      enableGenderBasedGrouping: json['enableGenderBasedGrouping'] ?? true,
      requireFaceVerification: json['requireFaceVerification'] ?? true,
      allowMixedGenderRides: json['allowMixedGenderRides'] ?? false,
      preferredGenders: List<String>.from(json['preferredGenders'] ?? []),
      minimumSafetyRating: json['minimumSafetyRating']?.toDouble() ?? 4.5,
      enableEmergencyContacts: json['enableEmergencyContacts'] ?? true,
      emergencyContactNumbers: List<String>.from(json['emergencyContactNumbers'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enableGenderBasedGrouping': enableGenderBasedGrouping,
      'requireFaceVerification': requireFaceVerification,
      'allowMixedGenderRides': allowMixedGenderRides,
      'preferredGenders': preferredGenders,
      'minimumSafetyRating': minimumSafetyRating,
      'enableEmergencyContacts': enableEmergencyContacts,
      'emergencyContactNumbers': emergencyContactNumbers,
    };
  }

  SafetyPreferences copyWith({
    bool? enableGenderBasedGrouping,
    bool? requireFaceVerification,
    bool? allowMixedGenderRides,
    List<String>? preferredGenders,
    double? minimumSafetyRating,
    bool? enableEmergencyContacts,
    List<String>? emergencyContactNumbers,
  }) {
    return SafetyPreferences(
      enableGenderBasedGrouping: enableGenderBasedGrouping ?? this.enableGenderBasedGrouping,
      requireFaceVerification: requireFaceVerification ?? this.requireFaceVerification,
      allowMixedGenderRides: allowMixedGenderRides ?? this.allowMixedGenderRides,
      preferredGenders: preferredGenders ?? this.preferredGenders,
      minimumSafetyRating: minimumSafetyRating ?? this.minimumSafetyRating,
      enableEmergencyContacts: enableEmergencyContacts ?? this.enableEmergencyContacts,
      emergencyContactNumbers: emergencyContactNumbers ?? this.emergencyContactNumbers,
    );
  }
}

class SafetyAlert {
  final String id;
  final String type;
  final String message;
  final DateTime timestamp;
  final String severity;
  final Map<String, dynamic> metadata;

  SafetyAlert({
    required this.id,
    required this.type,
    required this.message,
    required this.timestamp,
    required this.severity,
    required this.metadata,
  });

  factory SafetyAlert.fromJson(Map<String, dynamic> json) {
    return SafetyAlert(
      id: json['id'],
      type: json['type'],
      message: json['message'],
      timestamp: DateTime.parse(json['timestamp']),
      severity: json['severity'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity,
      'metadata': metadata,
    };
  }

  bool get isHighPriority => severity == 'high' || severity == 'critical';
}
