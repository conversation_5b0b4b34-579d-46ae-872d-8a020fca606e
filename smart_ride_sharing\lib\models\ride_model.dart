import 'driver_model.dart';
import 'location_model.dart';

enum RideStatus {
  searching,
  confirmed,
  driverAssigned,
  driverArrived,
  inProgress,
  completed,
  cancelled,
}

enum RideType {
  individual,
  shared,
  package, // For Backidge feature
}

class RideModel {
  final String id;
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final DriverModel driver;
  final double fare;
  final double distance;
  final int duration; // in minutes
  final RideStatus status;
  final RideType rideType;
  final DateTime createdAt;
  final DateTime? completedAt;
  final List<String>? sharedRiders; // For shared rides
  final String? packageDetails; // For package delivery
  final double? tip;
  final int? rating;
  final String? feedback;
  final bool aiMatched; // Whether this ride was AI-matched
  final List<String>? aiMatchingReasons; // Reasons for AI matching

  RideModel({
    required this.id,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.driver,
    required this.fare,
    required this.distance,
    required this.duration,
    required this.status,
    required this.rideType,
    required this.createdAt,
    this.completedAt,
    this.sharedRiders,
    this.packageDetails,
    this.tip,
    this.rating,
    this.feedback,
    this.aiMatched = false,
    this.aiMatchingReasons,
  });

  RideModel copyWith({
    String? id,
    LocationModel? pickupLocation,
    LocationModel? dropoffLocation,
    DriverModel? driver,
    double? fare,
    double? distance,
    int? duration,
    RideStatus? status,
    RideType? rideType,
    DateTime? createdAt,
    DateTime? completedAt,
    List<String>? sharedRiders,
    String? packageDetails,
    double? tip,
    int? rating,
    String? feedback,
    bool? aiMatched,
    List<String>? aiMatchingReasons,
  }) {
    return RideModel(
      id: id ?? this.id,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      driver: driver ?? this.driver,
      fare: fare ?? this.fare,
      distance: distance ?? this.distance,
      duration: duration ?? this.duration,
      status: status ?? this.status,
      rideType: rideType ?? this.rideType,
      createdAt: createdAt ?? this.createdAt,
      completedAt: completedAt ?? this.completedAt,
      sharedRiders: sharedRiders ?? this.sharedRiders,
      packageDetails: packageDetails ?? this.packageDetails,
      tip: tip ?? this.tip,
      rating: rating ?? this.rating,
      feedback: feedback ?? this.feedback,
      aiMatched: aiMatched ?? this.aiMatched,
      aiMatchingReasons: aiMatchingReasons ?? this.aiMatchingReasons,
    );
  }

  String get statusText {
    switch (status) {
      case RideStatus.searching:
        return 'Searching for driver...';
      case RideStatus.confirmed:
        return 'Ride confirmed';
      case RideStatus.driverAssigned:
        return 'Driver assigned';
      case RideStatus.driverArrived:
        return 'Driver arrived';
      case RideStatus.inProgress:
        return 'In progress';
      case RideStatus.completed:
        return 'Completed';
      case RideStatus.cancelled:
        return 'Cancelled';
    }
  }

  String get rideTypeText {
    switch (rideType) {
      case RideType.individual:
        return 'Individual Ride';
      case RideType.shared:
        return 'Shared Ride';
      case RideType.package:
        return 'Package Delivery';
    }
  }

  double get totalFare {
    return fare + (tip ?? 0.0);
  }

  bool get isSharedRide {
    return rideType == RideType.shared;
  }

  bool get isPackageDelivery {
    return rideType == RideType.package;
  }

  bool get canBeCancelled {
    return status == RideStatus.searching || 
           status == RideStatus.confirmed || 
           status == RideStatus.driverAssigned;
  }

  bool get isActive {
    return status != RideStatus.completed && status != RideStatus.cancelled;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'pickupLocation': pickupLocation.toJson(),
      'dropoffLocation': dropoffLocation.toJson(),
      'driver': driver.toJson(),
      'fare': fare,
      'distance': distance,
      'duration': duration,
      'status': status.toString(),
      'rideType': rideType.toString(),
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'sharedRiders': sharedRiders,
      'packageDetails': packageDetails,
      'tip': tip,
      'rating': rating,
      'feedback': feedback,
      'aiMatched': aiMatched,
      'aiMatchingReasons': aiMatchingReasons,
    };
  }

  factory RideModel.fromJson(Map<String, dynamic> json) {
    return RideModel(
      id: json['id'],
      pickupLocation: LocationModel.fromJson(json['pickupLocation']),
      dropoffLocation: LocationModel.fromJson(json['dropoffLocation']),
      driver: DriverModel.fromJson(json['driver']),
      fare: json['fare'].toDouble(),
      distance: json['distance'].toDouble(),
      duration: json['duration'],
      status: RideStatus.values.firstWhere(
        (e) => e.toString() == json['status'],
      ),
      rideType: RideType.values.firstWhere(
        (e) => e.toString() == json['rideType'],
      ),
      createdAt: DateTime.parse(json['createdAt']),
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
      sharedRiders: json['sharedRiders']?.cast<String>(),
      packageDetails: json['packageDetails'],
      tip: json['tip']?.toDouble(),
      rating: json['rating'],
      feedback: json['feedback'],
      aiMatched: json['aiMatched'] ?? false,
      aiMatchingReasons: json['aiMatchingReasons']?.cast<String>(),
    );
  }
}
