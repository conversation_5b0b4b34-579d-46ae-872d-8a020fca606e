import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/face_recognition_model.dart';
import '../../services/face_recognition_service.dart';
import '../../providers/auth_provider.dart';
import '../../utils/app_theme.dart';

class SafetyScreen extends StatefulWidget {
  @override
  _SafetyScreenState createState() => _SafetyScreenState();
}

class _SafetyScreenState extends State<SafetyScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  SafetyPreferences _safetyPrefs = SafetyPreferences();
  FaceRecognitionResult? _lastFaceRecognition;
  bool _isProcessing = false;
  List<SafetyAlert> _recentAlerts = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _animationController.forward();
    _loadSafetyData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadSafetyData() async {
    // Load safety preferences and recent alerts
    // In a real app, this would load from secure storage
    setState(() {
      _recentAlerts = []; // Will be populated with real data
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: 30),
                _buildFaceRecognitionCard(),
                SizedBox(height: 20),
                _buildSafetyPreferencesCard(),
                SizedBox(height: 20),
                _buildEmergencyContactsCard(),
                SizedBox(height: 20),
                _buildSafetyAlertsCard(),
                SizedBox(height: 20),
                _buildSafetyScoreCard(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.errorColor, AppTheme.warningColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(Icons.security, color: Colors.white, size: 28),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Safety Center',
                      style: GoogleFonts.poppins(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Your security is our priority',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFaceRecognitionCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.face_retouching_natural, color: AppTheme.aiPrimary, size: 24),
              SizedBox(width: 12),
              Text(
                'Face Recognition',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          if (_lastFaceRecognition != null) ...[
            _buildVerificationStatus(_lastFaceRecognition!),
            SizedBox(height: 16),
          ],
          ElevatedButton.icon(
            onPressed: _isProcessing ? null : _performFaceRecognition,
            icon: _isProcessing 
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                  )
                : Icon(Icons.camera_alt),
            label: Text(_isProcessing ? 'Processing...' : 'Verify Identity'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.aiPrimary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVerificationStatus(FaceRecognitionResult result) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: result.isVerified 
            ? AppTheme.successColor.withOpacity(0.1)
            : AppTheme.errorColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: result.isVerified 
              ? AppTheme.successColor.withOpacity(0.3)
              : AppTheme.errorColor.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            result.isVerified ? Icons.verified_user : Icons.warning,
            color: result.isVerified ? AppTheme.successColor : AppTheme.errorColor,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.isVerified ? 'Identity Verified' : 'Verification Failed',
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: result.isVerified ? AppTheme.successColor : AppTheme.errorColor,
                  ),
                ),
                Text(
                  'Confidence: ${result.confidenceText} (${(result.confidence * 100).toInt()}%)',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
                Text(
                  'Gender: ${result.detectedGender}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyPreferencesCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.tune, color: AppTheme.primaryColor, size: 24),
              SizedBox(width: 12),
              Text(
                'Safety Preferences',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 20),
          _buildSafetyToggle(
            'Gender-based Grouping',
            'Only ride with same gender',
            _safetyPrefs.enableGenderBasedGrouping,
            (value) => setState(() {
              _safetyPrefs = _safetyPrefs.copyWith(enableGenderBasedGrouping: value);
            }),
          ),
          _buildSafetyToggle(
            'Face Verification Required',
            'Verify identity before each ride',
            _safetyPrefs.requireFaceVerification,
            (value) => setState(() {
              _safetyPrefs = _safetyPrefs.copyWith(requireFaceVerification: value);
            }),
          ),
          _buildSafetyToggle(
            'Allow Mixed Gender Rides',
            'Override gender grouping when needed',
            _safetyPrefs.allowMixedGenderRides,
            (value) => setState(() {
              _safetyPrefs = _safetyPrefs.copyWith(allowMixedGenderRides: value);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyToggle(String title, String subtitle, bool value, Function(bool) onChanged) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppTheme.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.successColor,
          ),
        ],
      ),
    );
  }

  Widget _buildEmergencyContactsCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.emergency, color: AppTheme.errorColor, size: 24),
              SizedBox(width: 12),
              Text(
                'Emergency Contacts',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Text(
            '${_safetyPrefs.emergencyContactNumbers.length} contacts configured',
            style: GoogleFonts.poppins(
              color: AppTheme.textSecondary,
            ),
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _addEmergencyContact,
                  icon: Icon(Icons.add),
                  label: Text('Add Contact'),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _testEmergencyAlert,
                  icon: Icon(Icons.warning),
                  label: Text('Test Alert'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.errorColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyAlertsCard() {
    return Container(
      padding: EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.cardBackground,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppTheme.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.notifications_active, color: AppTheme.warningColor, size: 24),
              SizedBox(width: 12),
              Text(
                'Recent Safety Alerts',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          _recentAlerts.isEmpty
              ? Text(
                  'No recent safety alerts',
                  style: GoogleFonts.poppins(
                    color: AppTheme.textSecondary,
                  ),
                )
              : Column(
                  children: _recentAlerts.map((alert) => _buildAlertItem(alert)).toList(),
                ),
        ],
      ),
    );
  }

  Widget _buildAlertItem(SafetyAlert alert) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: alert.isHighPriority 
            ? AppTheme.errorColor.withOpacity(0.1)
            : AppTheme.warningColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            alert.isHighPriority ? Icons.error : Icons.warning,
            color: alert.isHighPriority ? AppTheme.errorColor : AppTheme.warningColor,
            size: 16,
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              alert.message,
              style: GoogleFonts.poppins(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSafetyScoreCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        double safetyScore = FaceRecognitionService.calculateSafetyScore(
          user: authProvider.user!,
          rideParticipants: [],
          safetyPrefs: _safetyPrefs,
          faceVerification: _lastFaceRecognition,
        );

        return Container(
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: AppTheme.cardBackground,
            borderRadius: BorderRadius.circular(20),
            boxShadow: AppTheme.cardShadow,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.shield, color: AppTheme.successColor, size: 24),
                  SizedBox(width: 12),
                  Text(
                    'Safety Score',
                    style: GoogleFonts.poppins(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16),
              Row(
                children: [
                  Text(
                    '${safetyScore.toInt()}',
                    style: GoogleFonts.poppins(
                      fontSize: 48,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.successColor,
                    ),
                  ),
                  Text(
                    '/100',
                    style: GoogleFonts.poppins(
                      fontSize: 24,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
              Text(
                safetyScore >= 80 ? 'Excellent Safety Profile' :
                safetyScore >= 60 ? 'Good Safety Profile' :
                'Needs Improvement',
                style: GoogleFonts.poppins(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _performFaceRecognition() async {
    setState(() => _isProcessing = true);

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final result = await FaceRecognitionService.performFaceRecognition(
        userId: authProvider.user!.id,
        imagePath: 'camera_capture_${DateTime.now().millisecondsSinceEpoch}.jpg',
        expectedGender: authProvider.user!.gender,
      );

      setState(() {
        _lastFaceRecognition = result;
        _isProcessing = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.isVerified 
              ? 'Identity verified successfully!' 
              : 'Verification failed. Please try again.'),
          backgroundColor: result.isVerified ? AppTheme.successColor : AppTheme.errorColor,
        ),
      );
    } catch (e) {
      setState(() => _isProcessing = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Face recognition failed: $e')),
      );
    }
  }

  void _addEmergencyContact() {
    // Show dialog to add emergency contact
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Emergency contact feature coming soon!')),
    );
  }

  void _testEmergencyAlert() {
    // Test emergency alert system
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Emergency alert test sent!'),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }
}
