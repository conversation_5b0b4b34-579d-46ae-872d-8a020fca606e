# Smart Ride Sharing

A modern, responsive Flutter app for ride sharing services with multiple transportation options.

## Features

- **Take a Ride**: Book individual rides
- **Backidge**: Package delivery service
- **Ride Sharing**: Shared transportation options

## Design Features

- Clean, modern UI with Material Design 3
- Smooth animations and transitions
- Responsive design for all screen sizes
- Gradient backgrounds and shadows
- Interactive button animations
- Custom SnackBar notifications

## Technical Stack

- **Framework**: Flutter 3.19.6
- **Language**: Dart
- **UI**: Material Design 3
- **Animations**: Custom AnimationController with staggered effects
- **Platform Support**: Web, Android, iOS, Windows Desktop

## Getting Started

### Prerequisites

- Flutter SDK 3.19.6 or higher
- Dart SDK
- For mobile development: Android Studio or Xcode
- For web development: Chrome browser

### Installation

1. Install dependencies:
```bash
flutter pub get
```

2. Run the app:

**For Web:**
```bash
flutter run -d chrome
```

**For Android:**
```bash
flutter run -d android
```

**For iOS:**
```bash
flutter run -d ios
```

**For Windows:**
```bash
flutter run -d windows
```

### Building for Production

**Web:**
```bash
flutter build web
```

**Android APK:**
```bash
flutter build apk
```

**iOS:**
```bash
flutter build ios
```

**Windows:**
```bash
flutter build windows
```

## Customization

### Colors
The app uses a blue color scheme. To change colors, modify the theme in `main.dart`:

```dart
theme: ThemeData(
  primarySwatch: Colors.blue,  // Change this
),
```

### Animations
Animation durations and curves can be adjusted in the respective widget classes.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
