class UserModel {
  final String id;
  final String name;
  final String email;
  final String photoUrl;
  final String phoneNumber;
  final double rating;
  final int totalRides;
  final DateTime joinDate;
  final bool isDriver;
  final String? gender;
  final bool faceVerified;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.photoUrl,
    required this.phoneNumber,
    required this.rating,
    required this.totalRides,
    required this.joinDate,
    this.isDriver = false,
    this.gender,
    this.faceVerified = false,
  });

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? photoUrl,
    String? phoneNumber,
    double? rating,
    int? totalRides,
    DateTime? joinDate,
    bool? isDriver,
    String? gender,
    bool? faceVerified,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      rating: rating ?? this.rating,
      totalRides: totalRides ?? this.totalRides,
      joinDate: joinDate ?? this.joinDate,
      isDriver: isDriver ?? this.isDriver,
      gender: gender ?? this.gender,
      faceVerified: faceVerified ?? this.faceVerified,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'photoUrl': photoUrl,
      'phoneNumber': phoneNumber,
      'rating': rating,
      'totalRides': totalRides,
      'joinDate': joinDate.toIso8601String(),
      'isDriver': isDriver,
      'gender': gender,
      'faceVerified': faceVerified,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      photoUrl: json['photoUrl'],
      phoneNumber: json['phoneNumber'],
      rating: json['rating'].toDouble(),
      totalRides: json['totalRides'],
      joinDate: DateTime.parse(json['joinDate']),
      isDriver: json['isDriver'] ?? false,
      gender: json['gender'],
      faceVerified: json['faceVerified'] ?? false,
    );
  }
}
