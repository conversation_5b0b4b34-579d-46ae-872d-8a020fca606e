import 'location_model.dart';

class DriverModel {
  final String id;
  final String name;
  final double rating;
  final String photoUrl;
  final String vehicleModel;
  final String licensePlate;
  final String phoneNumber;
  final LocationModel? currentLocation;
  final int? estimatedArrival; // in minutes
  final bool isAvailable;
  final int totalTrips;
  final String? vehicleColor;
  final String? vehicleYear;
  final bool isVerified;
  final List<String>? specialties; // e.g., ['female-only', 'package-delivery']

  DriverModel({
    required this.id,
    required this.name,
    required this.rating,
    required this.photoUrl,
    required this.vehicleModel,
    required this.licensePlate,
    required this.phoneNumber,
    this.currentLocation,
    this.estimatedArrival,
    this.isAvailable = true,
    this.totalTrips = 0,
    this.vehicleColor,
    this.vehicleYear,
    this.isVerified = false,
    this.specialties,
  });

  DriverModel copyWith({
    String? id,
    String? name,
    double? rating,
    String? photoUrl,
    String? vehicleModel,
    String? licensePlate,
    String? phoneNumber,
    LocationModel? currentLocation,
    int? estimatedArrival,
    bool? isAvailable,
    int? totalTrips,
    String? vehicleColor,
    String? vehicleYear,
    bool? isVerified,
    List<String>? specialties,
  }) {
    return DriverModel(
      id: id ?? this.id,
      name: name ?? this.name,
      rating: rating ?? this.rating,
      photoUrl: photoUrl ?? this.photoUrl,
      vehicleModel: vehicleModel ?? this.vehicleModel,
      licensePlate: licensePlate ?? this.licensePlate,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      currentLocation: currentLocation ?? this.currentLocation,
      estimatedArrival: estimatedArrival ?? this.estimatedArrival,
      isAvailable: isAvailable ?? this.isAvailable,
      totalTrips: totalTrips ?? this.totalTrips,
      vehicleColor: vehicleColor ?? this.vehicleColor,
      vehicleYear: vehicleYear ?? this.vehicleYear,
      isVerified: isVerified ?? this.isVerified,
      specialties: specialties ?? this.specialties,
    );
  }

  String get vehicleInfo {
    String info = vehicleModel;
    if (vehicleColor != null) {
      info = '$vehicleColor $info';
    }
    if (vehicleYear != null) {
      info = '$vehicleYear $info';
    }
    return info;
  }

  String get ratingText {
    return rating.toStringAsFixed(1);
  }

  String get estimatedArrivalText {
    if (estimatedArrival == null) return 'Unknown';
    if (estimatedArrival! <= 1) return '1 min';
    return '$estimatedArrival mins';
  }

  bool get isFemaleOnlyDriver {
    return specialties?.contains('female-only') ?? false;
  }

  bool get isPackageDeliveryDriver {
    return specialties?.contains('package-delivery') ?? false;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'rating': rating,
      'photoUrl': photoUrl,
      'vehicleModel': vehicleModel,
      'licensePlate': licensePlate,
      'phoneNumber': phoneNumber,
      'currentLocation': currentLocation?.toJson(),
      'estimatedArrival': estimatedArrival,
      'isAvailable': isAvailable,
      'totalTrips': totalTrips,
      'vehicleColor': vehicleColor,
      'vehicleYear': vehicleYear,
      'isVerified': isVerified,
      'specialties': specialties,
    };
  }

  factory DriverModel.fromJson(Map<String, dynamic> json) {
    return DriverModel(
      id: json['id'],
      name: json['name'],
      rating: json['rating'].toDouble(),
      photoUrl: json['photoUrl'],
      vehicleModel: json['vehicleModel'],
      licensePlate: json['licensePlate'],
      phoneNumber: json['phoneNumber'],
      currentLocation: json['currentLocation'] != null
          ? LocationModel.fromJson(json['currentLocation'])
          : null,
      estimatedArrival: json['estimatedArrival'],
      isAvailable: json['isAvailable'] ?? true,
      totalTrips: json['totalTrips'] ?? 0,
      vehicleColor: json['vehicleColor'],
      vehicleYear: json['vehicleYear'],
      isVerified: json['isVerified'] ?? false,
      specialties: json['specialties']?.cast<String>(),
    );
  }
}
