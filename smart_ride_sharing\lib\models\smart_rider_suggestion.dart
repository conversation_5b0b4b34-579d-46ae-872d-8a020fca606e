import 'location_model.dart';

/// 🎯 Smart Rider Suggestion Model
/// Represents an AI-generated suggestion for additional riders
/// Core part of the Smart Ride Sharing algorithm
class SmartRiderSuggestion {
  final String riderId;
  final String riderName;
  final double compatibility; // 0.0 to 1.0
  final int timeImpact; // Additional minutes
  final double costSavings; // Savings for all riders
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final DateTime estimatedPickupTime;
  final String? riderGender;
  final double? riderRating;
  final String? profilePhoto;
  final List<String> preferences;
  final bool isVerified;

  SmartRiderSuggestion({
    required this.riderId,
    required this.riderName,
    required this.compatibility,
    required this.timeImpact,
    required this.costSavings,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.estimatedPickupTime,
    this.riderGender,
    this.riderRating,
    this.profilePhoto,
    this.preferences = const [],
    this.isVerified = false,
  });

  /// Compatibility level description
  String get compatibilityLevel {
    if (compatibility >= 0.9) return 'Excellent Match';
    if (compatibility >= 0.8) return 'Great Match';
    if (compatibility >= 0.7) return 'Good Match';
    if (compatibility >= 0.6) return 'Fair Match';
    return 'Poor Match';
  }

  /// Time impact description
  String get timeImpactDescription {
    if (timeImpact <= 2) return 'Minimal delay';
    if (timeImpact <= 5) return 'Short delay';
    if (timeImpact <= 10) return 'Moderate delay';
    return 'Significant delay';
  }

  /// Cost savings description
  String get costSavingsDescription {
    return '\$${costSavings.toStringAsFixed(2)} saved for all riders';
  }

  /// Compatibility percentage
  int get compatibilityPercentage => (compatibility * 100).round();

  /// Is this a high-quality suggestion?
  bool get isHighQuality => compatibility >= 0.8 && timeImpact <= 5;

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'riderId': riderId,
      'riderName': riderName,
      'compatibility': compatibility,
      'timeImpact': timeImpact,
      'costSavings': costSavings,
      'pickupLocation': pickupLocation.toJson(),
      'dropoffLocation': dropoffLocation.toJson(),
      'estimatedPickupTime': estimatedPickupTime.toIso8601String(),
      'riderGender': riderGender,
      'riderRating': riderRating,
      'profilePhoto': profilePhoto,
      'preferences': preferences,
      'isVerified': isVerified,
    };
  }

  /// Create from JSON
  factory SmartRiderSuggestion.fromJson(Map<String, dynamic> json) {
    return SmartRiderSuggestion(
      riderId: json['riderId'],
      riderName: json['riderName'],
      compatibility: json['compatibility'].toDouble(),
      timeImpact: json['timeImpact'],
      costSavings: json['costSavings'].toDouble(),
      pickupLocation: LocationModel.fromJson(json['pickupLocation']),
      dropoffLocation: LocationModel.fromJson(json['dropoffLocation']),
      estimatedPickupTime: DateTime.parse(json['estimatedPickupTime']),
      riderGender: json['riderGender'],
      riderRating: json['riderRating']?.toDouble(),
      profilePhoto: json['profilePhoto'],
      preferences: List<String>.from(json['preferences'] ?? []),
      isVerified: json['isVerified'] ?? false,
    );
  }

  /// Create a copy with updated values
  SmartRiderSuggestion copyWith({
    String? riderId,
    String? riderName,
    double? compatibility,
    int? timeImpact,
    double? costSavings,
    LocationModel? pickupLocation,
    LocationModel? dropoffLocation,
    DateTime? estimatedPickupTime,
    String? riderGender,
    double? riderRating,
    String? profilePhoto,
    List<String>? preferences,
    bool? isVerified,
  }) {
    return SmartRiderSuggestion(
      riderId: riderId ?? this.riderId,
      riderName: riderName ?? this.riderName,
      compatibility: compatibility ?? this.compatibility,
      timeImpact: timeImpact ?? this.timeImpact,
      costSavings: costSavings ?? this.costSavings,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      dropoffLocation: dropoffLocation ?? this.dropoffLocation,
      estimatedPickupTime: estimatedPickupTime ?? this.estimatedPickupTime,
      riderGender: riderGender ?? this.riderGender,
      riderRating: riderRating ?? this.riderRating,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      preferences: preferences ?? this.preferences,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  @override
  String toString() {
    return 'SmartRiderSuggestion(riderId: $riderId, riderName: $riderName, compatibility: ${compatibilityPercentage}%, timeImpact: +${timeImpact}min, costSavings: \$${costSavings.toStringAsFixed(2)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmartRiderSuggestion && other.riderId == riderId;
  }

  @override
  int get hashCode => riderId.hashCode;
}

/// 🧑‍🤝‍🧑 Potential Rider Model
/// Represents a potential rider that could be added to a ride
class PotentialRider {
  final String id;
  final String name;
  final LocationModel pickup;
  final LocationModel dropoff;
  final String? gender;
  final double? rating;
  final DateTime requestTime;
  final List<String> preferences;
  final bool isVerified;

  PotentialRider({
    required this.id,
    required this.name,
    required this.pickup,
    required this.dropoff,
    this.gender,
    this.rating,
    required this.requestTime,
    this.preferences = const [],
    this.isVerified = false,
  });

  /// Distance from pickup to dropoff
  double get rideDistance {
    // Simple distance calculation (in real app, use proper geo calculation)
    double latDiff = pickup.latitude - dropoff.latitude;
    double lonDiff = pickup.longitude - dropoff.longitude;
    return (latDiff * latDiff + lonDiff * lonDiff) * 111; // Rough km conversion
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'pickup': pickup.toJson(),
      'dropoff': dropoff.toJson(),
      'gender': gender,
      'rating': rating,
      'requestTime': requestTime.toIso8601String(),
      'preferences': preferences,
      'isVerified': isVerified,
    };
  }

  /// Create from JSON
  factory PotentialRider.fromJson(Map<String, dynamic> json) {
    return PotentialRider(
      id: json['id'],
      name: json['name'],
      pickup: LocationModel.fromJson(json['pickup']),
      dropoff: LocationModel.fromJson(json['dropoff']),
      gender: json['gender'],
      rating: json['rating']?.toDouble(),
      requestTime: DateTime.parse(json['requestTime']),
      preferences: List<String>.from(json['preferences'] ?? []),
      isVerified: json['isVerified'] ?? false,
    );
  }

  @override
  String toString() {
    return 'PotentialRider(id: $id, name: $name, gender: $gender, rating: $rating)';
  }
}
