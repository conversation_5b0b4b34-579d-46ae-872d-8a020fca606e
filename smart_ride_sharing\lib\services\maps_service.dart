import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/location_model.dart';
import '../models/ride_model.dart';

class MapsService {
  static const String _apiKey = 'YOUR_GOOGLE_MAPS_API_KEY'; // Replace with actual API key

  // Enhanced real-time tracking with professional features
  static Timer? _trackingTimer;
  static StreamController<DriverLocation>? _locationController;
  static int _updateCount = 0;
  static DateTime? _trackingStartTime;

  // Professional tracking constants
  static const int TRACKING_UPDATE_INTERVAL_MS = 1000; // 1 second updates
  static const double MOVEMENT_SMOOTHING_FACTOR = 0.8;
  static const double SPEED_VARIATION_FACTOR = 0.15;
  
  /// Enhanced real-time tracking with professional features
  static Stream<DriverLocation> startRealTimeTracking(RideModel ride) {
    print('🗺️ Starting enhanced real-time tracking for ride ${ride.id}');
    print('📍 Route: ${ride.pickupLocation.shortAddress} → ${ride.dropoffLocation.shortAddress}');

    _locationController = StreamController<DriverLocation>.broadcast();
    _updateCount = 0;
    _trackingStartTime = DateTime.now();

    // Start professional driver movement simulation
    _simulateDriverMovement(ride);

    return _locationController!.stream;
  }

  /// Stop real-time tracking with cleanup
  static void stopRealTimeTracking() {
    print('🛑 Stopping real-time tracking');
    _trackingTimer?.cancel();
    _locationController?.close();
    _trackingTimer = null;
    _locationController = null;
    _updateCount = 0;
    _trackingStartTime = null;
  }

  /// Enhanced realistic driver movement simulation with professional features
  static void _simulateDriverMovement(RideModel ride) {
    LatLng currentPosition = LatLng(
      ride.driver.currentLocation!.latitude,
      ride.driver.currentLocation!.longitude,
    );

    LatLng destination = LatLng(
      ride.pickupLocation.latitude,
      ride.pickupLocation.longitude,
    );

    // If ride is in progress, move towards dropoff
    if (ride.status == RideStatus.inProgress) {
      destination = LatLng(
        ride.dropoffLocation.latitude,
        ride.dropoffLocation.longitude,
      );
    }

    LatLng previousPosition = currentPosition;
    int updateCount = 0;
    int totalUpdates = ride.duration * 60; // Convert minutes to seconds for realistic timing
    double totalDistance = _calculateDistance(currentPosition, destination);

    print('🚗 Driver starting journey: ${totalDistance.toStringAsFixed(1)}km in ${ride.duration} minutes');
    print('📱 Real-time updates every ${TRACKING_UPDATE_INTERVAL_MS}ms');
    
    _trackingTimer = Timer.periodic(Duration(milliseconds: TRACKING_UPDATE_INTERVAL_MS), (timer) {
      updateCount++;

      // Calculate smooth interpolated position with realistic movement
      double progress = updateCount / totalUpdates;
      LatLng interpolatedPosition = _interpolatePosition(currentPosition, destination, progress);

      // Add enhanced realistic movement variation with traffic simulation
      interpolatedPosition = _addEnhancedMovementVariation(interpolatedPosition, updateCount);

      // Calculate enhanced speed and bearing with smoothing
      double speed = _calculateEnhancedSpeed(previousPosition, interpolatedPosition);
      double bearing = _calculateSmoothedBearing(previousPosition, interpolatedPosition);

      // Create enhanced driver location with professional features
      DriverLocation location = DriverLocation(
        driverId: ride.driver.id,
        latitude: interpolatedPosition.latitude,
        longitude: interpolatedPosition.longitude,
        speed: speed,
        bearing: bearing,
        timestamp: DateTime.now(),
        accuracy: 3.0 + Random().nextDouble() * 4.0, // 3-7m accuracy (more realistic)
        isMoving: speed > 0.5, // More sensitive movement detection
      );

      _locationController?.add(location);

      // Log progress every 10 updates
      if (updateCount % 10 == 0) {
        print('📍 Driver progress: ${(progress * 100).toStringAsFixed(1)}% - Speed: ${speed.toStringAsFixed(1)} km/h');
      }

      // Update positions for next iteration
      previousPosition = currentPosition;
      currentPosition = interpolatedPosition;

      // Stop when destination is reached or after reasonable time
      if (progress >= 1.0 || updateCount >= totalUpdates * 1.5) {
        print('🏁 Driver reached destination!');
        timer.cancel();
      }
    });
  }

  /// Interpolate position between two points
  static LatLng _interpolatePosition(LatLng start, LatLng end, double progress) {
    progress = progress.clamp(0.0, 1.0);
    
    double lat = start.latitude + (end.latitude - start.latitude) * progress;
    double lng = start.longitude + (end.longitude - start.longitude) * progress;
    
    return LatLng(lat, lng);
  }

  /// Enhanced realistic movement variation with traffic simulation
  static LatLng _addEnhancedMovementVariation(LatLng position, int updateCount) {
    Random random = Random();

    // Base variation for GPS accuracy
    double baseLatVariation = (random.nextDouble() - 0.5) * 0.00005; // ~5m variation
    double baseLngVariation = (random.nextDouble() - 0.5) * 0.00005;

    // Traffic simulation - occasional stops and slowdowns
    double trafficFactor = 1.0;
    if (updateCount % 30 == 0) { // Traffic light every 30 seconds
      trafficFactor = 0.1; // Almost stopped
    } else if (updateCount % 15 == 0) { // Slow traffic
      trafficFactor = 0.5;
    }

    // Apply traffic factor to movement
    double latVariation = baseLatVariation * trafficFactor;
    double lngVariation = baseLngVariation * trafficFactor;

    return LatLng(
      position.latitude + latVariation,
      position.longitude + lngVariation,
    );
  }

  /// Enhanced speed calculation with smoothing and realistic variations
  static double _calculateEnhancedSpeed(LatLng pos1, LatLng pos2) {
    double distance = _calculateDistance(pos1, pos2);
    double baseSpeed = distance * 3600; // km/h (assuming 1 second interval)

    // Apply realistic speed variations
    Random random = Random();
    double speedVariation = 1.0 + (random.nextDouble() - 0.5) * SPEED_VARIATION_FACTOR;
    double enhancedSpeed = baseSpeed * speedVariation;

    // Clamp speed to realistic city driving range (0-80 km/h)
    enhancedSpeed = enhancedSpeed.clamp(0.0, 80.0);

    // Apply smoothing to prevent jerky movements
    enhancedSpeed = _smoothSpeed(enhancedSpeed);

    return enhancedSpeed;
  }

  /// Enhanced bearing calculation with smoothing for realistic vehicle movement
  static double _calculateSmoothedBearing(LatLng pos1, LatLng pos2) {
    double lat1Rad = pos1.latitude * pi / 180;
    double lat2Rad = pos2.latitude * pi / 180;
    double deltaLngRad = (pos2.longitude - pos1.longitude) * pi / 180;

    double y = sin(deltaLngRad) * cos(lat2Rad);
    double x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(deltaLngRad);

    double bearingRad = atan2(y, x);
    double bearing = (bearingRad * 180 / pi + 360) % 360;

    // Apply smoothing to prevent jerky rotations
    bearing = _smoothBearing(bearing);

    return bearing;
  }

  /// Calculate distance between two coordinates
  static double _calculateDistance(LatLng pos1, LatLng pos2) {
    const double earthRadius = 6371; // km
    
    double lat1Rad = pos1.latitude * pi / 180;
    double lat2Rad = pos2.latitude * pi / 180;
    double deltaLatRad = (pos2.latitude - pos1.latitude) * pi / 180;
    double deltaLngRad = (pos2.longitude - pos1.longitude) * pi / 180;
    
    double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLngRad / 2) * sin(deltaLngRad / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  /// Generate route polyline points
  static List<LatLng> generateRoutePolyline(List<LocationModel> waypoints) {
    List<LatLng> polylinePoints = [];
    
    for (int i = 0; i < waypoints.length - 1; i++) {
      LatLng start = LatLng(waypoints[i].latitude, waypoints[i].longitude);
      LatLng end = LatLng(waypoints[i + 1].latitude, waypoints[i + 1].longitude);
      
      // Generate intermediate points for smooth polyline
      List<LatLng> segmentPoints = _generateSegmentPoints(start, end, 10);
      polylinePoints.addAll(segmentPoints);
    }
    
    return polylinePoints;
  }

  /// Generate intermediate points between two coordinates
  static List<LatLng> _generateSegmentPoints(LatLng start, LatLng end, int numPoints) {
    List<LatLng> points = [];
    
    for (int i = 0; i <= numPoints; i++) {
      double progress = i / numPoints;
      LatLng point = _interpolatePosition(start, end, progress);
      points.add(point);
    }
    
    return points;
  }

  /// Create map markers for ride
  static Set<Marker> createRideMarkers(RideModel ride, {DriverLocation? driverLocation}) {
    Set<Marker> markers = {};
    
    // Pickup marker
    markers.add(Marker(
      markerId: MarkerId('pickup'),
      position: LatLng(ride.pickupLocation.latitude, ride.pickupLocation.longitude),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
      infoWindow: InfoWindow(
        title: 'Pickup Location',
        snippet: ride.pickupLocation.shortAddress,
      ),
    ));
    
    // Dropoff marker
    markers.add(Marker(
      markerId: MarkerId('dropoff'),
      position: LatLng(ride.dropoffLocation.latitude, ride.dropoffLocation.longitude),
      icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
      infoWindow: InfoWindow(
        title: 'Destination',
        snippet: ride.dropoffLocation.shortAddress,
      ),
    ));
    
    // Driver marker (real-time position)
    if (driverLocation != null) {
      markers.add(Marker(
        markerId: MarkerId('driver'),
        position: LatLng(driverLocation.latitude, driverLocation.longitude),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        rotation: driverLocation.bearing,
        infoWindow: InfoWindow(
          title: ride.driver.name,
          snippet: '${ride.driver.vehicleInfo} • ${driverLocation.speed.toStringAsFixed(1)} km/h',
        ),
      ));
    }
    
    // Shared riders markers (if applicable)
    if (ride.isSharedRide && ride.sharedRiders != null) {
      for (int i = 0; i < ride.sharedRiders!.length; i++) {
        // In a real app, you would have actual locations for shared riders
        markers.add(Marker(
          markerId: MarkerId('shared_rider_$i'),
          position: LatLng(
            ride.pickupLocation.latitude + (i * 0.001),
            ride.pickupLocation.longitude + (i * 0.001),
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange),
          infoWindow: InfoWindow(
            title: 'Shared Rider',
            snippet: ride.sharedRiders![i],
          ),
        ));
      }
    }
    
    return markers;
  }

  /// Create polylines for route
  static Set<Polyline> createRoutePolylines(RideModel ride) {
    Set<Polyline> polylines = {};
    
    List<LatLng> routePoints = generateRoutePolyline([
      ride.pickupLocation,
      ride.dropoffLocation,
    ]);
    
    polylines.add(Polyline(
      polylineId: PolylineId('main_route'),
      points: routePoints,
      color: Colors.blue,
      width: 5,
      patterns: [],
    ));
    
    return polylines;
  }

  /// Calculate optimal camera position for ride
  static CameraPosition calculateOptimalCameraPosition(RideModel ride, {DriverLocation? driverLocation}) {
    List<LatLng> points = [
      LatLng(ride.pickupLocation.latitude, ride.pickupLocation.longitude),
      LatLng(ride.dropoffLocation.latitude, ride.dropoffLocation.longitude),
    ];
    
    if (driverLocation != null) {
      points.add(LatLng(driverLocation.latitude, driverLocation.longitude));
    }
    
    LatLngBounds bounds = _calculateBounds(points);
    LatLng center = LatLng(
      (bounds.northeast.latitude + bounds.southwest.latitude) / 2,
      (bounds.northeast.longitude + bounds.southwest.longitude) / 2,
    );
    
    return CameraPosition(
      target: center,
      zoom: _calculateZoomLevel(bounds),
      bearing: 0,
      tilt: 0,
    );
  }

  /// Calculate bounds for multiple points
  static LatLngBounds _calculateBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;
    
    for (LatLng point in points) {
      minLat = min(minLat, point.latitude);
      maxLat = max(maxLat, point.latitude);
      minLng = min(minLng, point.longitude);
      maxLng = max(maxLng, point.longitude);
    }
    
    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  /// Calculate appropriate zoom level
  static double _calculateZoomLevel(LatLngBounds bounds) {
    double latDiff = bounds.northeast.latitude - bounds.southwest.latitude;
    double lngDiff = bounds.northeast.longitude - bounds.southwest.longitude;
    double maxDiff = max(latDiff, lngDiff);

    if (maxDiff > 0.1) return 10.0;
    if (maxDiff > 0.05) return 12.0;
    if (maxDiff > 0.01) return 14.0;
    if (maxDiff > 0.005) return 15.0;
    return 16.0;
  }

  // Helper methods for smoothing
  static double? _previousSpeed;
  static double? _previousBearing;

  static double _smoothSpeed(double speed) {
    if (_previousSpeed != null) {
      speed = (_previousSpeed! * MOVEMENT_SMOOTHING_FACTOR) +
              (speed * (1.0 - MOVEMENT_SMOOTHING_FACTOR));
    }
    _previousSpeed = speed;
    return speed;
  }

  static double _smoothBearing(double bearing) {
    if (_previousBearing != null) {
      // Handle bearing wraparound (359° to 1°)
      double diff = bearing - _previousBearing!;
      if (diff > 180) diff -= 360;
      if (diff < -180) diff += 360;

      bearing = _previousBearing! + (diff * (1.0 - MOVEMENT_SMOOTHING_FACTOR));
      bearing = (bearing + 360) % 360;
    }
    _previousBearing = bearing;
    return bearing;
  }
}

class DriverLocation {
  final String driverId;
  final double latitude;
  final double longitude;
  final double speed; // km/h
  final double bearing; // degrees
  final DateTime timestamp;
  final double accuracy; // meters
  final bool isMoving;

  DriverLocation({
    required this.driverId,
    required this.latitude,
    required this.longitude,
    required this.speed,
    required this.bearing,
    required this.timestamp,
    required this.accuracy,
    required this.isMoving,
  });

  LatLng get position => LatLng(latitude, longitude);
  
  String get speedText => '${speed.toStringAsFixed(1)} km/h';
  String get accuracyText => '±${accuracy.toStringAsFixed(0)}m';
}
