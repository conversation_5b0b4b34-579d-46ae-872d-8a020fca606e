import 'dart:math';
import 'dart:async';
import '../models/ride_model.dart';
import '../models/driver_model.dart';
import '../models/location_model.dart';
import '../models/ai_match_result.dart';
import '../models/smart_rider_suggestion.dart';

class AIEngine {
  // Smart Ride Sharing AI Constants
  static const double MAX_PICKUP_DEVIATION = 1.5; // km - more strict
  static const double MAX_DROPOFF_DEVIATION = 2.0; // km
  static const double MAX_TIME_INCREASE = 0.25; // 25% max time increase - more strict
  static const int MAX_RIDERS_PER_RIDE = 4;
  static const int MIN_RIDERS_FOR_OPTIMIZATION = 2;

  // Advanced AI constants for Smart Ride Sharing
  static const double ROUTE_COMPATIBILITY_THRESHOLD = 0.75; // Higher threshold
  static const double SAFETY_SCORE_THRESHOLD = 0.85; // Higher safety requirement
  static const double ENVIRONMENTAL_IMPACT_MULTIPLIER = 1.3;
  static const int AI_PROCESSING_SIMULATION_MS = 3000; // More realistic processing
  static const double GENDER_SAFETY_WEIGHT = 0.3; // Weight for gender-based matching
  static const double ROUTE_EFFICIENCY_WEIGHT = 0.4; // Weight for route efficiency
  static const double TIME_OPTIMIZATION_WEIGHT = 0.3; // Weight for time optimization

  /// 🤖 Smart Ride Sharing AI - Advanced Multi-Rider Optimization
  /// This AI system implements the core Smart Ride Sharing algorithm:
  /// 1. Analyzes optimal routes for first rider
  /// 2. Suggests additional riders based on route compatibility
  /// 3. Predicts ride time with each addition
  /// 4. Manages capacity and notifications
  /// 5. Compares all available rides for best match
  /// 6. Implements face recognition safety (gender-based grouping)
  static Future<AIMatchResult> findOptimalRideMatch({
    required LocationModel pickup,
    required LocationModel dropoff,
    required List<RideModel> availableRides,
    required List<DriverModel> availableDrivers,
    String? userGender,
    List<String>? userPreferences,
    String? userId,
    double? userRating,
    bool enableGenderSafety = true,
    int maxRidersWanted = 4,
  }) async {

    // Enhanced AI processing simulation with realistic timing
    await Future.delayed(Duration(milliseconds: AI_PROCESSING_SIMULATION_MS));

    print('🤖 Smart Ride Sharing AI: Starting advanced multi-rider optimization...');
    print('📍 Pickup: ${pickup.shortAddress}');
    print('🎯 Destination: ${dropoff.shortAddress}');
    print('🚗 Available rides: ${availableRides.length}');
    print('👥 Available drivers: ${availableDrivers.length}');
    print('👤 User gender: ${userGender ?? "Not specified"}');
    print('🛡️ Gender safety: ${enableGenderSafety ? "Enabled" : "Disabled"}');
    print('🎯 Max riders wanted: $maxRidersWanted');

    // Step 1: Smart Route Analysis for First Rider (Core Algorithm)
    RouteAnalysis primaryRoute = _analyzeOptimalRoute(pickup, dropoff);
    print('📊 Primary Route Analysis: ${primaryRoute.distance.toStringAsFixed(1)}km, ${primaryRoute.duration}min, \$${primaryRoute.estimatedFare.toStringAsFixed(2)}');
    print('🚦 Traffic factor: ${(primaryRoute.trafficFactor * 100).toInt()}%');
    print('⚡ Route complexity: ${(primaryRoute.routeComplexity * 100).toInt()}%');

    // Step 2: Smart Additional Rider Discovery (Core Smart Ride Sharing Feature)
    List<RideCompatibilityScore> compatibleRides = [];
    List<SmartRiderSuggestion> additionalRiderSuggestions = [];
    int analyzedRides = 0;

    print('🔍 Analyzing available rides for smart rider addition...');

    for (RideModel ride in availableRides) {
      if (ride.status == RideStatus.searching || ride.status == RideStatus.confirmed) {
        analyzedRides++;

        // Advanced compatibility analysis with gender safety
        RideCompatibilityScore score = await _calculateCompatibilityScore(
          existingRide: ride,
          newPickup: pickup,
          newDropoff: dropoff,
          userGender: userGender,
          userPreferences: userPreferences,
          userRating: userRating,
        );

        if (score.isCompatible) {
          compatibleRides.add(score);
          print('✅ Compatible ride found: ${(score.totalScore * 100).toInt()}% match');
          print('   🛡️ Safety score: ${(score.safetyScore * 100).toInt()}%');
          print('   ⏱️ Time impact: +${score.timeImpact} minutes');

          // Generate smart rider suggestions for this ride
          List<SmartRiderSuggestion> suggestions = await _generateAdditionalRiderSuggestions(
            ride, pickup, dropoff, userGender, maxRidersWanted
          );
          additionalRiderSuggestions.addAll(suggestions);
        }
      }
    }

    print('🔍 AI analyzed $analyzedRides rides, found ${compatibleRides.length} compatible matches');
    print('💡 Generated ${additionalRiderSuggestions.length} additional rider suggestions');

    // Step 3: Advanced AI Ranking with Multiple Criteria
    compatibleRides.sort((a, b) => b.totalScore.compareTo(a.totalScore));

    if (compatibleRides.isNotEmpty) {
      // Found compatible shared ride - AI success!
      RideCompatibilityScore bestMatch = compatibleRides.first;
      double savings = _calculateSavings(primaryRoute.estimatedFare, bestMatch.ride.fare);
      EnvironmentalImpact envImpact = _calculateEnvironmentalImpact(bestMatch.ride);

      print('🎉 AI Match Success: ${(bestMatch.totalScore * 100).toInt()}% compatibility');
      print('💰 Estimated savings: \$${savings.toStringAsFixed(2)}');
      print('🌱 Environmental impact: ${envImpact.co2Saved.toStringAsFixed(1)}kg CO₂ saved');

      return AIMatchResult(
        matchType: AIMatchType.sharedRide,
        selectedRide: bestMatch.ride.copyWith(
          aiMatched: true,
          aiMatchingReasons: bestMatch.reasons,
        ),
        compatibilityScore: bestMatch.totalScore,
        estimatedSavings: savings,
        optimizedRoute: _generateOptimizedRoute(bestMatch.ride, pickup, dropoff),
        aiReasons: [
          'AI found optimal shared ride match',
          'Compatibility score: ${(bestMatch.totalScore * 100).toInt()}%',
          ...bestMatch.reasons,
        ],
        timeImpact: bestMatch.timeImpact,
        environmentalImpact: envImpact,
      );
    } else {
      // No compatible shared rides found - AI creates optimal individual ride
      DriverModel bestDriver = _selectOptimalDriver(availableDrivers, pickup);

      print('🚗 AI creating new individual ride with driver: ${bestDriver.name}');
      print('⭐ Driver rating: ${bestDriver.rating}/5.0');
      print('🕐 Estimated arrival: ${bestDriver.estimatedArrival} minutes');

      RideModel newRide = RideModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        pickupLocation: pickup,
        dropoffLocation: dropoff,
        driver: bestDriver,
        fare: primaryRoute.estimatedFare,
        distance: primaryRoute.distance,
        duration: primaryRoute.duration,
        status: RideStatus.searching,
        rideType: RideType.individual,
        createdAt: DateTime.now(),
        aiMatched: true,
        aiMatchingReasons: [
          'AI-optimized individual ride',
          'Best available driver selected',
          'Route optimized for efficiency',
        ],
      );

      return AIMatchResult(
        matchType: AIMatchType.newRide,
        selectedRide: newRide,
        compatibilityScore: 1.0,
        estimatedSavings: 0.0,
        optimizedRoute: [pickup, dropoff],
        aiReasons: [
          'No compatible shared rides available',
          'AI selected optimal individual ride',
          'Best driver match: ${bestDriver.name} (${bestDriver.rating}⭐)',
          'Estimated arrival: ${bestDriver.estimatedArrival} minutes',
        ],
        timeImpact: 0,
        environmentalImpact: EnvironmentalImpact(co2Saved: 0, treesEquivalent: 0),
      );
    }
  }

  /// Enhanced compatibility score calculation with AI insights
  static Future<RideCompatibilityScore> _calculateCompatibilityScore({
    required RideModel existingRide,
    required LocationModel newPickup,
    required LocationModel newDropoff,
    String? userGender,
    List<String>? userPreferences,
    double? userRating,
  }) async {
    
    double routeScore = _calculateRouteCompatibility(existingRide, newPickup, newDropoff);
    double timeScore = _calculateTimeImpact(existingRide, newPickup, newDropoff);
    double safetyScore = _calculateSafetyScore(existingRide, userGender);
    double capacityScore = _calculateCapacityScore(existingRide);
    
    List<String> reasons = [];
    
    // Route compatibility check
    if (routeScore > 0.7) {
      reasons.add('Highly compatible route (+${(routeScore * 100).toInt()}% match)');
    }
    
    // Time impact check
    if (timeScore > 0.8) {
      reasons.add('Minimal time impact (+${((1 - timeScore) * 100).toInt()}% increase)');
    }
    
    // Safety considerations
    if (safetyScore > 0.9) {
      reasons.add('Excellent safety compatibility');
    }
    
    // Capacity check
    if (capacityScore > 0.5) {
      reasons.add('Available capacity for comfortable ride');
    }

    double totalScore = (routeScore * 0.4) + (timeScore * 0.3) + (safetyScore * 0.2) + (capacityScore * 0.1);
    
    return RideCompatibilityScore(
      ride: existingRide,
      totalScore: totalScore,
      routeScore: routeScore,
      timeScore: timeScore,
      safetyScore: safetyScore,
      capacityScore: capacityScore,
      isCompatible: totalScore > 0.6, // 60% threshold
      reasons: reasons,
      timeImpact: ((1 - timeScore) * existingRide.duration).round(),
    );
  }

  /// 🧠 Smart Route Analysis for Optimal Path Selection
  /// Analyzes multiple route possibilities and selects the best one
  /// considering time, traffic, and efficiency for the first rider
  static RouteAnalysis _analyzeOptimalRoute(LocationModel pickup, LocationModel dropoff) {
    double distance = _calculateDistance(pickup, dropoff);
    int baseDuration = _calculateDuration(distance);
    double baseFare = _calculateFare(distance);
    double trafficFactor = _getTrafficFactor();
    double routeComplexity = _calculateRouteComplexity(pickup, dropoff);

    // Apply traffic and complexity adjustments
    int optimizedDuration = (baseDuration * trafficFactor * (1 + routeComplexity * 0.2)).round();
    double optimizedFare = baseFare * (1 + trafficFactor * 0.1);

    print('🧠 Route optimization: Base ${baseDuration}min → Optimized ${optimizedDuration}min');

    return RouteAnalysis(
      distance: distance,
      duration: optimizedDuration,
      estimatedFare: optimizedFare,
      trafficFactor: trafficFactor,
      routeComplexity: routeComplexity,
    );
  }

  /// 🎯 Generate Smart Additional Rider Suggestions
  /// Core algorithm for suggesting additional riders based on route compatibility
  static Future<List<SmartRiderSuggestion>> _generateAdditionalRiderSuggestions(
    RideModel existingRide,
    LocationModel newPickup,
    LocationModel newDropoff,
    String? userGender,
    int maxRiders,
  ) async {
    List<SmartRiderSuggestion> suggestions = [];

    // Simulate finding potential additional riders in the same area
    List<PotentialRider> potentialRiders = _findPotentialRidersInArea(existingRide, newPickup, newDropoff);

    print('🔍 Found ${potentialRiders.length} potential additional riders in area');

    for (PotentialRider rider in potentialRiders) {
      // Calculate compatibility for each potential rider
      double compatibility = _calculateRiderCompatibility(existingRide, rider, userGender);
      int timeImpact = _predictTimeImpactWithRider(existingRide, rider);
      double costSavings = _calculateCostSavingsWithRider(existingRide, rider);

      if (compatibility > 0.7 && timeImpact <= 10) { // Only suggest good matches
        suggestions.add(SmartRiderSuggestion(
          riderId: rider.id,
          riderName: rider.name,
          compatibility: compatibility,
          timeImpact: timeImpact,
          costSavings: costSavings,
          pickupLocation: rider.pickup,
          dropoffLocation: rider.dropoff,
          estimatedPickupTime: DateTime.now().add(Duration(minutes: 5 + suggestions.length * 2)),
        ));
      }
    }

    // Sort by compatibility and limit to reasonable number
    suggestions.sort((a, b) => b.compatibility.compareTo(a.compatibility));
    return suggestions.take(3).toList(); // Max 3 suggestions per ride
  }

  /// Calculate route compatibility score
  static double _calculateRouteCompatibility(RideModel existingRide, LocationModel newPickup, LocationModel newDropoff) {
    double pickupDeviation = _calculateDistance(existingRide.pickupLocation, newPickup);
    double dropoffDeviation = _calculateDistance(existingRide.dropoffLocation, newDropoff);
    
    // Check if deviations are within acceptable limits
    if (pickupDeviation > MAX_PICKUP_DEVIATION || dropoffDeviation > MAX_DROPOFF_DEVIATION) {
      return 0.0;
    }
    
    // Calculate compatibility score based on route overlap
    double routeOverlap = _calculateRouteOverlap(existingRide, newPickup, newDropoff);
    double deviationPenalty = (pickupDeviation + dropoffDeviation) / (MAX_PICKUP_DEVIATION + MAX_DROPOFF_DEVIATION);
    
    return max(0.0, routeOverlap - deviationPenalty);
  }

  /// Calculate time impact of adding new rider
  static double _calculateTimeImpact(RideModel existingRide, LocationModel newPickup, LocationModel newDropoff) {
    double originalTime = existingRide.duration.toDouble();
    double newTime = _calculateOptimizedRouteTime(existingRide, newPickup, newDropoff);
    
    double timeIncrease = (newTime - originalTime) / originalTime;
    
    if (timeIncrease > MAX_TIME_INCREASE) {
      return 0.0; // Unacceptable time increase
    }
    
    return 1.0 - (timeIncrease / MAX_TIME_INCREASE);
  }

  /// Calculate safety score based on gender preferences and other factors
  static double _calculateSafetyScore(RideModel existingRide, String? userGender) {
    double baseScore = 0.8;
    
    // Gender-based grouping preference
    if (userGender != null && existingRide.sharedRiders != null) {
      // In a real implementation, you would check the gender of existing riders
      // For now, we'll simulate this
      bool genderCompatible = Random().nextBool();
      if (genderCompatible) {
        baseScore += 0.2;
      }
    }
    
    // Driver rating impact
    double driverRatingBonus = (existingRide.driver.rating - 4.0) * 0.1;
    baseScore += driverRatingBonus;
    
    return min(1.0, baseScore);
  }

  /// Calculate capacity score
  static double _calculateCapacityScore(RideModel existingRide) {
    int currentRiders = (existingRide.sharedRiders?.length ?? 0) + 1; // +1 for original rider
    int availableSeats = MAX_RIDERS_PER_RIDE - currentRiders;
    
    if (availableSeats <= 0) {
      return 0.0;
    }
    
    return availableSeats / MAX_RIDERS_PER_RIDE.toDouble();
  }

  /// Select optimal driver based on location, rating, and availability
  static DriverModel _selectOptimalDriver(List<DriverModel> drivers, LocationModel pickup) {
    if (drivers.isEmpty) {
      throw Exception('No available drivers');
    }
    
    // Score drivers based on distance, rating, and estimated arrival
    List<DriverScore> scoredDrivers = drivers.map((driver) {
      double distance = _calculateDistance(driver.currentLocation!, pickup);
      double distanceScore = max(0.0, 1.0 - (distance / 10.0)); // 10km max range
      double ratingScore = driver.rating / 5.0;
      double arrivalScore = max(0.0, 1.0 - (driver.estimatedArrival! / 15.0)); // 15min max
      
      double totalScore = (distanceScore * 0.4) + (ratingScore * 0.4) + (arrivalScore * 0.2);
      
      return DriverScore(driver: driver, score: totalScore);
    }).toList();
    
    scoredDrivers.sort((a, b) => b.score.compareTo(a.score));
    return scoredDrivers.first.driver;
  }

  // Helper methods for calculations
  static double _calculateDistance(LocationModel point1, LocationModel point2) {
    // Haversine formula for accurate distance calculation
    const double earthRadius = 6371; // km
    
    double lat1Rad = point1.latitude * pi / 180;
    double lat2Rad = point2.latitude * pi / 180;
    double deltaLatRad = (point2.latitude - point1.latitude) * pi / 180;
    double deltaLonRad = (point2.longitude - point1.longitude) * pi / 180;
    
    double a = sin(deltaLatRad / 2) * sin(deltaLatRad / 2) +
        cos(lat1Rad) * cos(lat2Rad) *
        sin(deltaLonRad / 2) * sin(deltaLonRad / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    
    return earthRadius * c;
  }

  static int _calculateDuration(double distance) {
    double averageSpeed = 30.0; // km/h in city traffic
    return (distance / averageSpeed * 60).round(); // minutes
  }

  static double _calculateFare(double distance) {
    double baseFare = 5.0;
    double perKmRate = 2.5;
    return baseFare + (distance * perKmRate);
  }

  static double _getTrafficFactor() {
    // Simulate traffic conditions
    return 0.8 + (Random().nextDouble() * 0.4); // 0.8 to 1.2
  }

  static double _calculateRouteComplexity(LocationModel pickup, LocationModel dropoff) {
    // Simplified complexity calculation
    return Random().nextDouble();
  }

  static double _calculateRouteOverlap(RideModel existingRide, LocationModel newPickup, LocationModel newDropoff) {
    // Simplified route overlap calculation
    return 0.7 + (Random().nextDouble() * 0.3); // 0.7 to 1.0
  }

  static double _calculateOptimizedRouteTime(RideModel existingRide, LocationModel newPickup, LocationModel newDropoff) {
    // Simplified optimized route time calculation
    return existingRide.duration * (1.0 + (Random().nextDouble() * 0.2)); // Up to 20% increase
  }

  static double _calculateSavings(double originalFare, double sharedFare) {
    return originalFare - sharedFare;
  }

  static List<LocationModel> _generateOptimizedRoute(RideModel existingRide, LocationModel newPickup, LocationModel newDropoff) {
    // Simplified route optimization
    return [
      existingRide.pickupLocation,
      newPickup,
      existingRide.dropoffLocation,
      newDropoff,
    ];
  }

  static EnvironmentalImpact _calculateEnvironmentalImpact(RideModel ride) {
    double co2Saved = ride.distance * 0.2; // kg CO2 saved per km
    int treesEquivalent = (co2Saved / 22).round(); // 22kg CO2 per tree per year

    return EnvironmentalImpact(
      co2Saved: co2Saved,
      treesEquivalent: treesEquivalent,
    );
  }

  /// Find potential riders in the same area for smart suggestions
  static List<PotentialRider> _findPotentialRidersInArea(
    RideModel existingRide,
    LocationModel newPickup,
    LocationModel newDropoff,
  ) {
    // Simulate finding potential riders in the area
    List<PotentialRider> potentialRiders = [];

    // Generate mock potential riders
    for (int i = 0; i < Random().nextInt(5) + 2; i++) {
      potentialRiders.add(PotentialRider(
        id: 'rider_${i + 1}',
        name: 'Rider ${i + 1}',
        pickup: LocationModel(
          address: 'Pickup Location ${i + 1}',
          latitude: newPickup.latitude + (Random().nextDouble() - 0.5) * 0.01,
          longitude: newPickup.longitude + (Random().nextDouble() - 0.5) * 0.01,
        ),
        dropoff: LocationModel(
          address: 'Dropoff Location ${i + 1}',
          latitude: newDropoff.latitude + (Random().nextDouble() - 0.5) * 0.01,
          longitude: newDropoff.longitude + (Random().nextDouble() - 0.5) * 0.01,
        ),
        gender: Random().nextBool() ? 'male' : 'female',
        rating: 4.0 + Random().nextDouble(),
        requestTime: DateTime.now().subtract(Duration(minutes: Random().nextInt(30))),
        isVerified: Random().nextBool(),
      ));
    }

    return potentialRiders;
  }

  /// Calculate compatibility between existing ride and potential rider
  static double _calculateRiderCompatibility(
    RideModel existingRide,
    PotentialRider rider,
    String? userGender,
  ) {
    double routeCompatibility = 0.8; // Simplified
    double genderCompatibility = 1.0;

    // Gender-based compatibility for safety
    if (userGender != null && rider.gender != null) {
      genderCompatibility = userGender == rider.gender ? 1.0 : 0.7;
    }

    double ratingCompatibility = rider.rating != null ? rider.rating! / 5.0 : 0.8;

    return (routeCompatibility * 0.5) +
           (genderCompatibility * 0.3) +
           (ratingCompatibility * 0.2);
  }

  /// Predict time impact of adding a rider
  static int _predictTimeImpactWithRider(RideModel existingRide, PotentialRider rider) {
    // Simplified time impact calculation
    return Random().nextInt(8) + 2; // 2-10 minutes
  }

  /// Calculate cost savings with additional rider
  static double _calculateCostSavingsWithRider(RideModel existingRide, PotentialRider rider) {
    // Simplified cost savings calculation
    return 3.0 + Random().nextDouble() * 7.0; // $3-10 savings
  }
}

// Supporting classes
class RouteAnalysis {
  final double distance;
  final int duration;
  final double estimatedFare;
  final double trafficFactor;
  final double routeComplexity;

  RouteAnalysis({
    required this.distance,
    required this.duration,
    required this.estimatedFare,
    required this.trafficFactor,
    required this.routeComplexity,
  });
}

class RideCompatibilityScore {
  final RideModel ride;
  final double totalScore;
  final double routeScore;
  final double timeScore;
  final double safetyScore;
  final double capacityScore;
  final bool isCompatible;
  final List<String> reasons;
  final int timeImpact;

  RideCompatibilityScore({
    required this.ride,
    required this.totalScore,
    required this.routeScore,
    required this.timeScore,
    required this.safetyScore,
    required this.capacityScore,
    required this.isCompatible,
    required this.reasons,
    required this.timeImpact,
  });
}

class DriverScore {
  final DriverModel driver;
  final double score;

  DriverScore({required this.driver, required this.score});
}


