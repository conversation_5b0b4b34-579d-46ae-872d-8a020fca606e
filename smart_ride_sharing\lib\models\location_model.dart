class LocationModel {
  final String address;
  final double latitude;
  final double longitude;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final String? placeId;

  LocationModel({
    required this.address,
    required this.latitude,
    required this.longitude,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.placeId,
  });

  LocationModel copyWith({
    String? address,
    double? latitude,
    double? longitude,
    String? city,
    String? state,
    String? country,
    String? postalCode,
    String? placeId,
  }) {
    return LocationModel(
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      city: city ?? this.city,
      state: state ?? this.state,
      country: country ?? this.country,
      postalCode: postalCode ?? this.postalCode,
      placeId: placeId ?? this.placeId,
    );
  }

  String get shortAddress {
    // Return a shortened version of the address
    List<String> parts = address.split(',');
    if (parts.length > 2) {
      return '${parts[0]}, ${parts[1]}';
    }
    return address;
  }

  String get fullAddress {
    return address;
  }

  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'city': city,
      'state': state,
      'country': country,
      'postalCode': postalCode,
      'placeId': placeId,
    };
  }

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      city: json['city'],
      state: json['state'],
      country: json['country'],
      postalCode: json['postalCode'],
      placeId: json['placeId'],
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel &&
        other.latitude == latitude &&
        other.longitude == longitude;
  }

  @override
  int get hashCode {
    return latitude.hashCode ^ longitude.hashCode;
  }

  @override
  String toString() {
    return 'LocationModel(address: $address, lat: $latitude, lng: $longitude)';
  }
}
