import 'dart:async';
import 'package:flutter/material.dart';
import '../models/ride_model.dart';
import '../models/driver_model.dart';
import '../models/location_model.dart';
import '../models/ai_match_result.dart';
import '../services/ai_engine.dart';

class RideProvider with ChangeNotifier {
  List<RideModel> _availableRides = [];
  List<RideModel> _rideHistory = [];
  RideModel? _currentRide;
  List<DriverModel> _nearbyDrivers = [];
  bool _isSearchingRide = false;
  bool _isLoading = false;
  String _selectedRideType = 'individual';
  AIMatchResult? _lastAIMatchResult;
  List<RiderJoinRequest> _pendingJoinRequests = [];

  // Getters
  List<RideModel> get availableRides => _availableRides;
  List<RideModel> get rideHistory => _rideHistory;
  RideModel? get currentRide => _currentRide;
  List<DriverModel> get nearbyDrivers => _nearbyDrivers;
  bool get isSearchingRide => _isSearchingRide;
  bool get isLoading => _isLoading;
  String get selectedRideType => _selectedRideType;
  AIMatchResult? get lastAIMatchResult => _lastAIMatchResult;
  List<RiderJoinRequest> get pendingJoinRequests => _pendingJoinRequests;

  RideProvider() {
    _initializeMockData();
  }

  void _initializeMockData() {
    // Mock ride history
    _rideHistory = [
      RideModel(
        id: '1',
        pickupLocation: LocationModel(
          address: '123 Main St, Downtown',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        dropoffLocation: LocationModel(
          address: '456 Oak Ave, Uptown',
          latitude: 40.7589,
          longitude: -73.9851,
        ),
        driver: DriverModel(
          id: '1',
          name: 'Mike Johnson',
          rating: 4.8,
          photoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
          vehicleModel: 'Toyota Camry',
          licensePlate: 'ABC-123',
          phoneNumber: '+**********',
        ),
        fare: 15.50,
        distance: 5.2,
        duration: 18,
        status: RideStatus.completed,
        rideType: RideType.individual,
        createdAt: DateTime.now().subtract(Duration(days: 2)),
        completedAt: DateTime.now().subtract(Duration(days: 2, hours: -1)),
      ),
      RideModel(
        id: '2',
        pickupLocation: LocationModel(
          address: '789 Pine St, Midtown',
          latitude: 40.7505,
          longitude: -73.9934,
        ),
        dropoffLocation: LocationModel(
          address: '321 Elm St, Downtown',
          latitude: 40.7128,
          longitude: -74.0060,
        ),
        driver: DriverModel(
          id: '2',
          name: 'Sarah Wilson',
          rating: 4.9,
          photoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
          vehicleModel: 'Honda Accord',
          licensePlate: 'XYZ-789',
          phoneNumber: '+**********',
        ),
        fare: 22.75,
        distance: 7.8,
        duration: 25,
        status: RideStatus.completed,
        rideType: RideType.shared,
        sharedRiders: ['John Doe', 'Jane Smith'],
        createdAt: DateTime.now().subtract(Duration(days: 5)),
        completedAt: DateTime.now().subtract(Duration(days: 5, hours: -1)),
      ),
    ];

    // Mock nearby drivers
    _nearbyDrivers = [
      DriverModel(
        id: '3',
        name: 'Alex Chen',
        rating: 4.7,
        photoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        vehicleModel: 'Nissan Altima',
        licensePlate: 'DEF-456',
        phoneNumber: '+**********',
        currentLocation: LocationModel(
          address: 'Near you',
          latitude: 40.7300,
          longitude: -74.0000,
        ),
        estimatedArrival: 3,
      ),
      DriverModel(
        id: '4',
        name: 'Maria Garcia',
        rating: 4.9,
        photoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        vehicleModel: 'Hyundai Elantra',
        licensePlate: 'GHI-789',
        phoneNumber: '+**********',
        currentLocation: LocationModel(
          address: 'Near you',
          latitude: 40.7250,
          longitude: -74.0100,
        ),
        estimatedArrival: 5,
      ),
    ];
  }

  void setRideType(String rideType) {
    _selectedRideType = rideType;
    notifyListeners();
  }

  Future<void> searchForRide(LocationModel pickup, LocationModel dropoff, {String? userGender}) async {
    _isSearchingRide = true;
    _isLoading = true;
    notifyListeners();

    try {
      // Use advanced AI engine for ride matching
      _lastAIMatchResult = await AIEngine.findOptimalRideMatch(
        pickup: pickup,
        dropoff: dropoff,
        availableRides: _availableRides,
        availableDrivers: _nearbyDrivers,
        userGender: userGender,
        userPreferences: [], // Could be expanded with user preferences
      );

      _currentRide = _lastAIMatchResult!.selectedRide;

      // If it's a shared ride match, simulate rider join requests
      if (_lastAIMatchResult!.isSharedRide && _selectedRideType == 'shared') {
        _simulateRiderJoinRequests();
      }

    } catch (e) {
      print('Error searching for ride: $e');
    }

    _isSearchingRide = false;
    _isLoading = false;
    notifyListeners();
  }

  Future<void> _findSharedRideMatches(LocationModel pickup, LocationModel dropoff) async {
    // Simulate AI matching algorithm
    await Future.delayed(Duration(seconds: 2));

    // Check for existing rides that can accommodate new rider
    List<RideModel> compatibleRides = _availableRides.where((ride) {
      return ride.rideType == RideType.shared &&
             ride.status == RideStatus.searching &&
             _isRouteCompatible(ride, pickup, dropoff);
    }).toList();

    if (compatibleRides.isNotEmpty) {
      // Found compatible shared ride
      RideModel bestMatch = compatibleRides.first;
      _currentRide = bestMatch.copyWith(
        sharedRiders: [...(bestMatch.sharedRiders ?? []), 'You'],
      );
    } else {
      // Create new shared ride
      _currentRide = RideModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        pickupLocation: pickup,
        dropoffLocation: dropoff,
        driver: _nearbyDrivers.first,
        fare: _calculateSharedFare(pickup, dropoff),
        distance: _calculateDistance(pickup, dropoff),
        duration: _calculateDuration(pickup, dropoff),
        status: RideStatus.searching,
        rideType: RideType.shared,
        createdAt: DateTime.now(),
      );
    }
  }

  Future<void> _findIndividualRide(LocationModel pickup, LocationModel dropoff) async {
    _currentRide = RideModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      pickupLocation: pickup,
      dropoffLocation: dropoff,
      driver: _nearbyDrivers.first,
      fare: _calculateIndividualFare(pickup, dropoff),
      distance: _calculateDistance(pickup, dropoff),
      duration: _calculateDuration(pickup, dropoff),
      status: RideStatus.searching,
      rideType: RideType.individual,
      createdAt: DateTime.now(),
    );
  }

  bool _isRouteCompatible(RideModel existingRide, LocationModel pickup, LocationModel dropoff) {
    // AI logic to determine route compatibility
    // This is a simplified version - in real app, this would use complex algorithms
    double pickupDistance = _calculateDistanceBetweenPoints(
      existingRide.pickupLocation, pickup
    );
    double dropoffDistance = _calculateDistanceBetweenPoints(
      existingRide.dropoffLocation, dropoff
    );
    
    // Compatible if pickup and dropoff are within 2km of existing route
    return pickupDistance <= 2.0 && dropoffDistance <= 2.0;
  }

  double _calculateDistance(LocationModel pickup, LocationModel dropoff) {
    // Simplified distance calculation
    return _calculateDistanceBetweenPoints(pickup, dropoff);
  }

  double _calculateDistanceBetweenPoints(LocationModel point1, LocationModel point2) {
    // Simplified distance calculation (in real app, use proper geo calculations)
    double latDiff = (point1.latitude - point2.latitude).abs();
    double lonDiff = (point1.longitude - point2.longitude).abs();
    return (latDiff + lonDiff) * 111; // Rough km conversion
  }

  int _calculateDuration(LocationModel pickup, LocationModel dropoff) {
    double distance = _calculateDistance(pickup, dropoff);
    return (distance / 0.5).round(); // Assume 30 km/h average speed
  }

  double _calculateIndividualFare(LocationModel pickup, LocationModel dropoff) {
    double distance = _calculateDistance(pickup, dropoff);
    return 5.0 + (distance * 2.5); // Base fare + distance rate
  }

  double _calculateSharedFare(LocationModel pickup, LocationModel dropoff) {
    double individualFare = _calculateIndividualFare(pickup, dropoff);
    return individualFare * 0.7; // 30% discount for shared rides
  }

  Future<void> confirmRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: RideStatus.confirmed);
      notifyListeners();
      
      // Simulate driver assignment and arrival
      await Future.delayed(Duration(seconds: 2));
      _currentRide = _currentRide!.copyWith(status: RideStatus.driverAssigned);
      notifyListeners();
    }
  }

  Future<void> cancelRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: RideStatus.cancelled);
      _rideHistory.insert(0, _currentRide!);
      _currentRide = null;
      notifyListeners();
    }
  }

  Future<void> completeRide() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(
        status: RideStatus.completed,
        completedAt: DateTime.now(),
      );
      _rideHistory.insert(0, _currentRide!);
      _currentRide = null;
      notifyListeners();
    }
  }

  void clearCurrentRide() {
    _currentRide = null;
    _lastAIMatchResult = null;
    notifyListeners();
  }

  /// Simulate rider join requests for shared rides
  void _simulateRiderJoinRequests() {
    // Simulate 1-3 riders wanting to join the ride
    int numRequests = 1 + (DateTime.now().millisecond % 3);

    for (int i = 0; i < numRequests; i++) {
      Future.delayed(Duration(seconds: 5 + (i * 10)), () {
        if (_currentRide != null && _currentRide!.isSharedRide) {
          RiderJoinRequest request = RiderJoinRequest(
            requestId: 'req_${DateTime.now().millisecondsSinceEpoch}_$i',
            riderId: 'rider_$i',
            riderName: ['Sarah Johnson', 'Mike Chen', 'Emma Wilson'][i % 3],
            riderPhotoUrl: [
              'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
              'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
              'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150'
            ][i % 3],
            riderRating: 4.5 + (i * 0.2),
            pickupLocation: LocationModel(
              address: 'Near your route',
              latitude: _currentRide!.pickupLocation.latitude + (i * 0.001),
              longitude: _currentRide!.pickupLocation.longitude + (i * 0.001),
            ),
            dropoffLocation: LocationModel(
              address: 'Compatible destination',
              latitude: _currentRide!.dropoffLocation.latitude + (i * 0.001),
              longitude: _currentRide!.dropoffLocation.longitude + (i * 0.001),
            ),
            compatibilityScore: 0.85 + (i * 0.05),
            reasons: [
              'Highly compatible route (+90% match)',
              'Minimal time impact (+2 min)',
              'Excellent safety rating',
              'Saves \$${(3 + i).toStringAsFixed(2)} for both riders'
            ],
            estimatedTimeImpact: 2 + i,
            estimatedSavings: 3.0 + i,
            requestTime: DateTime.now(),
            expiresAt: DateTime.now().add(Duration(minutes: 5)),
          );

          _pendingJoinRequests.add(request);
          notifyListeners();

          // Show notification to user
          _showRiderJoinNotification(request);
        }
      });
    }
  }

  /// Handle rider join request response
  Future<void> respondToJoinRequest(String requestId, bool accepted) async {
    RiderJoinRequest? request = _pendingJoinRequests.firstWhere(
      (req) => req.requestId == requestId,
    );

    if (request != null) {
      _pendingJoinRequests.removeWhere((req) => req.requestId == requestId);

      if (accepted && _currentRide != null) {
        // Add rider to current ride
        List<String> updatedRiders = List.from(_currentRide!.sharedRiders ?? []);
        updatedRiders.add(request.riderName);

        _currentRide = _currentRide!.copyWith(
          sharedRiders: updatedRiders,
          fare: _currentRide!.fare - request.estimatedSavings,
          duration: _currentRide!.duration + request.estimatedTimeImpact,
        );

        // Update AI match result
        if (_lastAIMatchResult != null) {
          _lastAIMatchResult = AIMatchResult(
            matchType: _lastAIMatchResult!.matchType,
            selectedRide: _currentRide!,
            compatibilityScore: _lastAIMatchResult!.compatibilityScore,
            estimatedSavings: _lastAIMatchResult!.estimatedSavings + request.estimatedSavings,
            optimizedRoute: _lastAIMatchResult!.optimizedRoute,
            aiReasons: [
              ..._lastAIMatchResult!.aiReasons,
              'Added compatible rider: ${request.riderName}'
            ],
            timeImpact: _lastAIMatchResult!.timeImpact + request.estimatedTimeImpact,
            environmentalImpact: _lastAIMatchResult!.environmentalImpact,
          );
        }
      }

      notifyListeners();
    }
  }

  /// Show notification for rider join request
  void _showRiderJoinNotification(RiderJoinRequest request) {
    // In a real app, this would trigger a push notification or in-app notification
    print('🔔 New rider wants to join: ${request.riderName} (${request.compatibilityScore * 100}% match)');
  }

  /// Get AI insights for current ride
  Map<String, dynamic> getAIInsights() {
    if (_lastAIMatchResult == null) return {};

    return {
      'matchType': _lastAIMatchResult!.matchTypeText,
      'compatibilityScore': _lastAIMatchResult!.compatibilityScore,
      'confidenceLevel': _lastAIMatchResult!.confidenceLevelText,
      'estimatedSavings': _lastAIMatchResult!.estimatedSavings,
      'timeImpact': _lastAIMatchResult!.timeImpact,
      'environmentalImpact': _lastAIMatchResult!.environmentalImpact,
      'aiReasons': _lastAIMatchResult!.aiReasons,
      'isSharedRide': _lastAIMatchResult!.isSharedRide,
    };
  }

  /// Start ride with live tracking
  Future<void> startRideWithTracking() async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: RideStatus.inProgress);
      notifyListeners();

      // In a real app, this would start GPS tracking and real-time updates
      print('🚗 Started live tracking for ride ${_currentRide!.id}');
    }
  }

  /// Update ride status with AI monitoring
  Future<void> updateRideStatus(RideStatus newStatus) async {
    if (_currentRide != null) {
      _currentRide = _currentRide!.copyWith(status: newStatus);

      // AI monitoring for safety and optimization
      if (newStatus == RideStatus.inProgress) {
        _startAIMonitoring();
      }

      notifyListeners();
    }
  }

  /// Start AI monitoring during ride
  void _startAIMonitoring() {
    // Simulate AI monitoring features
    Timer.periodic(Duration(seconds: 30), (timer) {
      if (_currentRide?.status != RideStatus.inProgress) {
        timer.cancel();
        return;
      }

      // Simulate AI safety checks, route optimization, etc.
      print('🤖 AI monitoring: Route optimal, safety checks passed');
    });
  }
}
