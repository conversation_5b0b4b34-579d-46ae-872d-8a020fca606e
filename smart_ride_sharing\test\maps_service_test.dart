import 'package:flutter_test/flutter_test.dart';
import 'package:smart_ride_sharing/services/maps_service.dart';
import 'package:smart_ride_sharing/models/ride_model.dart';
import 'package:smart_ride_sharing/models/driver_model.dart';
import 'package:smart_ride_sharing/models/location_model.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:async';

void main() {
  group('Enhanced Maps Service Tests', () {
    late RideModel testRide;
    late DriverModel testDriver;
    late LocationModel pickupLocation;
    late LocationModel dropoffLocation;

    setUp(() {
      pickupLocation = LocationModel(
        address: '123 Main St, Downtown',
        latitude: 40.7128,
        longitude: -74.0060,
        city: 'New York',
        state: 'NY',
      );

      dropoffLocation = LocationModel(
        address: '456 Broadway, Midtown',
        latitude: 40.7589,
        longitude: -73.9851,
        city: 'New York',
        state: 'NY',
      );

      testDriver = DriverModel(
        id: 'driver_1',
        name: '<PERSON>',
        rating: 4.8,
        photoUrl: 'https://example.com/photo.jpg',
        vehicleModel: 'Toyota Camry',
        licensePlate: 'ABC123',
        phoneNumber: '+**********',
        currentLocation: pickupLocation,
        estimatedArrival: 5,
        isAvailable: true,
        totalTrips: 150,
        vehicleColor: 'Blue',
        vehicleYear: '2020',
        isVerified: true,
      );

      testRide = RideModel(
        id: 'ride_1',
        pickupLocation: pickupLocation,
        dropoffLocation: dropoffLocation,
        driver: testDriver,
        fare: 25.0,
        distance: 5.2,
        duration: 15,
        status: RideStatus.inProgress,
        rideType: RideType.individual,
        createdAt: DateTime.now(),
      );
    });

    tearDown(() {
      // Clean up tracking after each test
      MapsService.stopRealTimeTracking();
    });

    test('Maps Service should start real-time tracking', () async {
      // Start tracking
      Stream<DriverLocation> trackingStream = MapsService.startRealTimeTracking(testRide);
      
      // Verify stream is created
      expect(trackingStream, isNotNull);
      
      // Listen to first location update
      DriverLocation? firstLocation;
      StreamSubscription subscription = trackingStream.listen((location) {
        firstLocation = location;
      });
      
      // Wait for first update
      await Future.delayed(Duration(seconds: 2));
      
      // Verify location update received
      expect(firstLocation, isNotNull);
      expect(firstLocation!.driverId, equals(testDriver.id));
      expect(firstLocation!.latitude, isA<double>());
      expect(firstLocation!.longitude, isA<double>());
      expect(firstLocation!.speed, greaterThanOrEqualTo(0.0));
      expect(firstLocation!.bearing, greaterThanOrEqualTo(0.0));
      expect(firstLocation!.timestamp, isNotNull);
      
      await subscription.cancel();
    });

    test('Maps Service should stop real-time tracking', () {
      // Start tracking
      Stream<DriverLocation> trackingStream = MapsService.startRealTimeTracking(testRide);
      expect(trackingStream, isNotNull);
      
      // Stop tracking
      MapsService.stopRealTimeTracking();
      
      // Verify tracking is stopped (no exceptions should be thrown)
      expect(() => MapsService.stopRealTimeTracking(), returnsNormally);
    });

    test('Maps Service should create ride markers correctly', () {
      // Create markers without driver location
      Set<Marker> markers = MapsService.createRideMarkers(testRide);
      
      // Should have pickup and dropoff markers
      expect(markers.length, equals(2));
      
      // Verify pickup marker
      Marker? pickupMarker = markers.firstWhere(
        (marker) => marker.markerId.value == 'pickup',
        orElse: () => throw Exception('Pickup marker not found'),
      );
      expect(pickupMarker.position.latitude, equals(pickupLocation.latitude));
      expect(pickupMarker.position.longitude, equals(pickupLocation.longitude));
      
      // Verify dropoff marker
      Marker? dropoffMarker = markers.firstWhere(
        (marker) => marker.markerId.value == 'dropoff',
        orElse: () => throw Exception('Dropoff marker not found'),
      );
      expect(dropoffMarker.position.latitude, equals(dropoffLocation.latitude));
      expect(dropoffMarker.position.longitude, equals(dropoffLocation.longitude));
    });

    test('Maps Service should create markers with driver location', () {
      DriverLocation driverLocation = DriverLocation(
        driverId: testDriver.id,
        latitude: 40.7150,
        longitude: -74.0070,
        speed: 25.5,
        bearing: 45.0,
        timestamp: DateTime.now(),
        accuracy: 5.0,
        isMoving: true,
      );
      
      // Create markers with driver location
      Set<Marker> markers = MapsService.createRideMarkers(testRide, driverLocation: driverLocation);
      
      // Should have pickup, dropoff, and driver markers
      expect(markers.length, equals(3));
      
      // Verify driver marker
      Marker? driverMarker = markers.firstWhere(
        (marker) => marker.markerId.value == 'driver',
        orElse: () => throw Exception('Driver marker not found'),
      );
      expect(driverMarker.position.latitude, equals(driverLocation.latitude));
      expect(driverMarker.position.longitude, equals(driverLocation.longitude));
      expect(driverMarker.rotation, equals(driverLocation.bearing));
    });

    test('Maps Service should create route polylines', () {
      // Create polylines
      Set<Polyline> polylines = MapsService.createRoutePolylines(testRide);
      
      // Should have main route polyline
      expect(polylines.length, equals(1));
      
      Polyline mainRoute = polylines.first;
      expect(mainRoute.polylineId.value, equals('main_route'));
      expect(mainRoute.points, isNotEmpty);
      expect(mainRoute.color, isNotNull);
      expect(mainRoute.width, greaterThan(0));
    });

    test('Maps Service should calculate optimal camera position', () {
      DriverLocation driverLocation = DriverLocation(
        driverId: testDriver.id,
        latitude: 40.7150,
        longitude: -74.0070,
        speed: 25.5,
        bearing: 45.0,
        timestamp: DateTime.now(),
        accuracy: 5.0,
        isMoving: true,
      );
      
      // Calculate camera position
      CameraPosition cameraPosition = MapsService.calculateOptimalCameraPosition(
        testRide,
        driverLocation: driverLocation,
      );
      
      // Verify camera position
      expect(cameraPosition.target, isNotNull);
      expect(cameraPosition.zoom, greaterThan(0.0));
      expect(cameraPosition.bearing, greaterThanOrEqualTo(0.0));
      expect(cameraPosition.tilt, greaterThanOrEqualTo(0.0));
    });

    test('Maps Service should generate route polyline points', () {
      List<LocationModel> waypoints = [pickupLocation, dropoffLocation];
      
      // Generate polyline points
      List<LatLng> polylinePoints = MapsService.generateRoutePolyline(waypoints);
      
      // Verify polyline points
      expect(polylinePoints, isNotEmpty);
      expect(polylinePoints.length, greaterThan(waypoints.length));
      
      // First point should be pickup location
      expect(polylinePoints.first.latitude, equals(pickupLocation.latitude));
      expect(polylinePoints.first.longitude, equals(pickupLocation.longitude));
      
      // Last point should be dropoff location
      expect(polylinePoints.last.latitude, equals(dropoffLocation.latitude));
      expect(polylinePoints.last.longitude, equals(dropoffLocation.longitude));
    });

    test('DriverLocation should provide correct text representations', () {
      DriverLocation driverLocation = DriverLocation(
        driverId: testDriver.id,
        latitude: 40.7150,
        longitude: -74.0070,
        speed: 25.5,
        bearing: 45.0,
        timestamp: DateTime.now(),
        accuracy: 5.0,
        isMoving: true,
      );
      
      // Test text representations
      expect(driverLocation.speedText, equals('25.5 km/h'));
      expect(driverLocation.accuracyText, equals('±5m'));
      expect(driverLocation.position, isA<LatLng>());
      expect(driverLocation.position.latitude, equals(40.7150));
      expect(driverLocation.position.longitude, equals(-74.0070));
    });
  });
}
