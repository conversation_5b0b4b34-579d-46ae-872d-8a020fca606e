import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_model.dart';

class AuthProvider with ChangeNotifier {
  UserModel? _user;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  UserModel? get user => _user;
  bool get isAuthenticated => _isAuthenticated;
  bool get isLoading => _isLoading;

  AuthProvider() {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;
      
      if (isLoggedIn) {
        // Load user data from preferences
        final userName = prefs.getString('userName') ?? 'User';
        final userEmail = prefs.getString('userEmail') ?? '<EMAIL>';
        final userPhoto = prefs.getString('userPhoto') ?? '';
        
        _user = UserModel(
          id: '1',
          name: userName,
          email: userEmail,
          photoUrl: userPhoto,
          phoneNumber: '+**********',
          rating: 4.8,
          totalRides: 156,
          joinDate: DateTime.now().subtract(Duration(days: 365)),
        );
        _isAuthenticated = true;
      }
    } catch (e) {
      print('Error checking auth status: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<bool> signInWithGoogle() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate Google Sign-In delay
      await Future.delayed(Duration(seconds: 2));
      
      // Fake successful authentication
      _user = UserModel(
        id: '1',
        name: 'John Doe',
        email: '<EMAIL>',
        photoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        phoneNumber: '+**********',
        rating: 4.8,
        totalRides: 156,
        joinDate: DateTime.now().subtract(Duration(days: 365)),
        gender: 'male',
        faceVerified: true,
      );
      
      _isAuthenticated = true;
      
      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('isLoggedIn', true);
      await prefs.setString('userName', _user!.name);
      await prefs.setString('userEmail', _user!.email);
      await prefs.setString('userPhoto', _user!.photoUrl);
      
      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<bool> signInWithEmail(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Simulate email sign-in delay
      await Future.delayed(Duration(seconds: 1));
      
      // Fake validation (accept any email/password for demo)
      if (email.isNotEmpty && password.length >= 6) {
        _user = UserModel(
          id: '2',
          name: email.split('@')[0].toUpperCase(),
          email: email,
          photoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
          phoneNumber: '+**********',
          rating: 4.5,
          totalRides: 89,
          joinDate: DateTime.now().subtract(Duration(days: 180)),
          gender: 'female',
          faceVerified: false,
        );
        
        _isAuthenticated = true;
        
        // Save to preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isLoggedIn', true);
        await prefs.setString('userName', _user!.name);
        await prefs.setString('userEmail', _user!.email);
        await prefs.setString('userPhoto', _user!.photoUrl);
        
        _isLoading = false;
        notifyListeners();
        return true;
      } else {
        _isLoading = false;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _isLoading = false;
      notifyListeners();
      return false;
    }
  }

  Future<void> signOut() async {
    _isLoading = true;
    notifyListeners();

    try {
      // Clear preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      _user = null;
      _isAuthenticated = false;
    } catch (e) {
      print('Error signing out: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  void updateUserProfile({
    String? name,
    String? phoneNumber,
    String? photoUrl,
  }) {
    if (_user != null) {
      _user = _user!.copyWith(
        name: name ?? _user!.name,
        phoneNumber: phoneNumber ?? _user!.phoneNumber,
        photoUrl: photoUrl ?? _user!.photoUrl,
      );
      notifyListeners();
      
      // Update preferences
      _updatePreferences();
    }
  }

  Future<void> _updatePreferences() async {
    if (_user != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('userName', _user!.name);
      await prefs.setString('userPhoto', _user!.photoUrl);
    }
  }
}
