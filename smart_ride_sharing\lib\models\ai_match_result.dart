import 'ride_model.dart';
import 'location_model.dart';

enum AIMatchType {
  sharedRide,
  newRide,
  noMatch,
}

class AIMatchResult {
  final AIMatchType matchType;
  final RideModel selectedRide;
  final double compatibilityScore;
  final double estimatedSavings;
  final List<LocationModel> optimizedRoute;
  final List<String> aiReasons;
  final int timeImpact; // in minutes
  final EnvironmentalImpact environmentalImpact;
  final List<RideAlternative>? alternatives;
  final AIConfidenceLevel confidenceLevel;

  AIMatchResult({
    required this.matchType,
    required this.selectedRide,
    required this.compatibilityScore,
    required this.estimatedSavings,
    required this.optimizedRoute,
    required this.aiReasons,
    required this.timeImpact,
    required this.environmentalImpact,
    this.alternatives,
    AIConfidenceLevel? confidenceLevel,
  }) : confidenceLevel = confidenceLevel ?? _calculateConfidenceLevel(compatibilityScore);

  static AIConfidenceLevel _calculateConfidenceLevel(double score) {
    if (score >= 0.9) return AIConfidenceLevel.veryHigh;
    if (score >= 0.8) return AIConfidenceLevel.high;
    if (score >= 0.7) return AIConfidenceLevel.medium;
    if (score >= 0.6) return AIConfidenceLevel.low;
    return AIConfidenceLevel.veryLow;
  }

  String get confidenceLevelText {
    switch (confidenceLevel) {
      case AIConfidenceLevel.veryHigh:
        return 'Excellent Match';
      case AIConfidenceLevel.high:
        return 'Great Match';
      case AIConfidenceLevel.medium:
        return 'Good Match';
      case AIConfidenceLevel.low:
        return 'Fair Match';
      case AIConfidenceLevel.veryLow:
        return 'Poor Match';
    }
  }

  String get matchTypeText {
    switch (matchType) {
      case AIMatchType.sharedRide:
        return 'Smart Shared Ride';
      case AIMatchType.newRide:
        return 'Individual Ride';
      case AIMatchType.noMatch:
        return 'No Match Found';
    }
  }

  bool get isSharedRide => matchType == AIMatchType.sharedRide;
  bool get hasSignificantSavings => estimatedSavings > 5.0;
  bool get hasMinimalTimeImpact => timeImpact <= 5;

  Map<String, dynamic> toJson() {
    return {
      'matchType': matchType.toString(),
      'selectedRide': selectedRide.toJson(),
      'compatibilityScore': compatibilityScore,
      'estimatedSavings': estimatedSavings,
      'optimizedRoute': optimizedRoute.map((loc) => loc.toJson()).toList(),
      'aiReasons': aiReasons,
      'timeImpact': timeImpact,
      'environmentalImpact': environmentalImpact.toJson(),
      'confidenceLevel': confidenceLevel.toString(),
    };
  }
}

class RideAlternative {
  final RideModel ride;
  final double score;
  final String reason;
  final double estimatedSavings;
  final int timeImpact;

  RideAlternative({
    required this.ride,
    required this.score,
    required this.reason,
    required this.estimatedSavings,
    required this.timeImpact,
  });
}

class EnvironmentalImpact {
  final double co2Saved; // kg
  final int treesEquivalent;
  final double fuelSaved; // liters
  final String impactLevel;

  EnvironmentalImpact({
    required this.co2Saved,
    required this.treesEquivalent,
    double? fuelSaved,
    String? impactLevel,
  }) : fuelSaved = fuelSaved ?? co2Saved * 0.4,
       impactLevel = impactLevel ?? _calculateImpactLevel(co2Saved);

  static String _calculateImpactLevel(double co2Saved) {
    if (co2Saved >= 5.0) return 'High Impact';
    if (co2Saved >= 2.0) return 'Medium Impact';
    if (co2Saved >= 0.5) return 'Low Impact';
    return 'Minimal Impact';
  }

  Map<String, dynamic> toJson() {
    return {
      'co2Saved': co2Saved,
      'treesEquivalent': treesEquivalent,
      'fuelSaved': fuelSaved,
      'impactLevel': impactLevel,
    };
  }
}

enum AIConfidenceLevel {
  veryHigh,
  high,
  medium,
  low,
  veryLow,
}

class AIDecisionContext {
  final String userId;
  final String? userGender;
  final List<String> userPreferences;
  final double userRating;
  final int userTotalRides;
  final DateTime requestTime;
  final WeatherCondition? weather;
  final TrafficCondition trafficCondition;
  final bool isPeakHour;

  AIDecisionContext({
    required this.userId,
    this.userGender,
    this.userPreferences = const [],
    required this.userRating,
    required this.userTotalRides,
    required this.requestTime,
    this.weather,
    required this.trafficCondition,
    required this.isPeakHour,
  });
}

enum WeatherCondition {
  sunny,
  cloudy,
  rainy,
  snowy,
  stormy,
}

enum TrafficCondition {
  light,
  moderate,
  heavy,
  severe,
}

class AINotification {
  final String id;
  final String title;
  final String message;
  final AINotificationType type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final bool requiresUserAction;

  AINotification({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.data,
    this.requiresUserAction = false,
  });
}

enum AINotificationType {
  rideMatchFound,
  riderRequestToJoin,
  routeOptimization,
  safetyAlert,
  environmentalUpdate,
  costSavingsAlert,
}

class RiderJoinRequest {
  final String requestId;
  final String riderId;
  final String riderName;
  final String riderPhotoUrl;
  final double riderRating;
  final LocationModel pickupLocation;
  final LocationModel dropoffLocation;
  final double compatibilityScore;
  final List<String> reasons;
  final int estimatedTimeImpact;
  final double estimatedSavings;
  final DateTime requestTime;
  final DateTime expiresAt;

  RiderJoinRequest({
    required this.requestId,
    required this.riderId,
    required this.riderName,
    required this.riderPhotoUrl,
    required this.riderRating,
    required this.pickupLocation,
    required this.dropoffLocation,
    required this.compatibilityScore,
    required this.reasons,
    required this.estimatedTimeImpact,
    required this.estimatedSavings,
    required this.requestTime,
    required this.expiresAt,
  });

  bool get isExpired => DateTime.now().isAfter(expiresAt);
  bool get isHighCompatibility => compatibilityScore >= 0.8;
  bool get hasMinimalTimeImpact => estimatedTimeImpact <= 3;
}
